''' <summary>
''' اختبارات وحدة للتأكد من صحة الحسابات
''' </summary>
Public Class CalculatorTests
    
    ''' <summary>
    ''' اختبار حساب أيام التكليف
    ''' </summary>
    Public Shared Sub TestAssignmentDaysCalculation()
        Console.WriteLine("=== اختبار حساب أيام التكليف ===")
        
        ' اختبار 1: تكليف لمدة أسبوع (5 أيام عمل)
        Dim startDate1 As Date = New Date(2024, 1, 1) ' الاثنين
        Dim endDate1 As Date = New Date(2024, 1, 5)   ' الجمعة
        Dim days1 As Integer = AssignmentCalculator.CalculateAssignmentDays(startDate1, endDate1, True, False)
        Console.WriteLine($"اختبار 1: من {startDate1:dd/MM/yyyy} إلى {endDate1:dd/MM/yyyy}")
        Console.WriteLine($"النتيجة المتوقعة: 4 أيام (استثناء الجمعة)")
        Console.WriteLine($"النتيجة الفعلية: {days1} أيام")
        Console.WriteLine($"النتيجة: {If(days1 = 4, "نجح ✓", "فشل ✗")}")
        Console.WriteLine()
        
        ' اختبار 2: تكليف لمدة شهر
        Dim startDate2 As Date = New Date(2024, 1, 1)
        Dim endDate2 As Date = New Date(2024, 1, 31)
        Dim days2 As Integer = AssignmentCalculator.CalculateAssignmentDays(startDate2, endDate2, True, True)
        Console.WriteLine($"اختبار 2: من {startDate2:dd/MM/yyyy} إلى {endDate2:dd/MM/yyyy}")
        Console.WriteLine($"النتيجة الفعلية: {days2} أيام (استثناء الجمع والعطل)")
        Console.WriteLine()
        
        ' اختبار 3: حساب إجمالي الانتداب
        Dim totalAllowance As Decimal = AssignmentCalculator.CalculateTotalAllowance(10, 300)
        Console.WriteLine($"اختبار 3: حساب إجمالي الانتداب")
        Console.WriteLine($"10 أيام × 300 ريال = {totalAllowance} ريال")
        Console.WriteLine($"النتيجة: {If(totalAllowance = 3000, "نجح ✓", "فشل ✗")}")
        Console.WriteLine()
    End Sub
    
    ''' <summary>
    ''' اختبار حساب أيام الإجازة
    ''' </summary>
    Public Shared Sub TestLeaveDaysCalculation()
        Console.WriteLine("=== اختبار حساب أيام الإجازة ===")
        
        ' اختبار 1: إجازة لمدة أسبوع (5 أيام عمل)
        Dim startDate1 As Date = New Date(2024, 1, 1) ' الاثنين
        Dim endDate1 As Date = New Date(2024, 1, 7)   ' الأحد
        Dim days1 As Integer = LeaveCalculator.CalculateLeaveDays(startDate1, endDate1, True, False)
        Console.WriteLine($"اختبار 1: من {startDate1:dd/MM/yyyy} إلى {endDate1:dd/MM/yyyy}")
        Console.WriteLine($"النتيجة المتوقعة: 5 أيام (استثناء الجمعة والسبت)")
        Console.WriteLine($"النتيجة الفعلية: {days1} أيام")
        Console.WriteLine($"النتيجة: {If(days1 = 5, "نجح ✓", "فشل ✗")}")
        Console.WriteLine()
        
        ' اختبار 2: فحص الرصيد الكافي
        Dim employee As New Employee("001", "أحمد محمد", "1234567890") With {
            .AnnualLeaveBalance = 20
        }
        
        Dim hasSufficient1 As Boolean = LeaveCalculator.HasSufficientBalance(employee, LeaveType.Annual, 15)
        Dim hasSufficient2 As Boolean = LeaveCalculator.HasSufficientBalance(employee, LeaveType.Annual, 25)
        
        Console.WriteLine($"اختبار 2: فحص الرصيد الكافي")
        Console.WriteLine($"رصيد الموظف: 20 يوم")
        Console.WriteLine($"طلب 15 يوم: {If(hasSufficient1, "كافي ✓", "غير كافي ✗")}")
        Console.WriteLine($"طلب 25 يوم: {If(Not hasSufficient2, "غير كافي ✓", "كافي ✗")}")
        Console.WriteLine()
    End Sub
    
    ''' <summary>
    ''' اختبار الانتداب حسب المرتبة
    ''' </summary>
    Public Shared Sub TestDailyAllowanceByGrade()
        Console.WriteLine("=== اختبار الانتداب حسب المرتبة ===")
        
        Dim grades() As String = {"الأولى", "الثالثة", "الخامسة", "العاشرة"}
        Dim expectedValues() As Decimal = {500, 400, 300, 150}
        
        For i As Integer = 0 To grades.Length - 1
            Dim allowance As Decimal = AssignmentCalculator.GetDailyAllowanceByGrade(grades(i))
            Console.WriteLine($"المرتبة {grades(i)}: {allowance} ريال")
            Console.WriteLine($"النتيجة: {If(allowance = expectedValues(i), "نجح ✓", "فشل ✗")}")
        Next
        Console.WriteLine()
    End Sub
    
    ''' <summary>
    ''' اختبار العطل الرسمية
    ''' </summary>
    Public Shared Sub TestOfficialHolidays()
        Console.WriteLine("=== اختبار العطل الرسمية ===")
        
        ' اليوم الوطني
        Dim nationalDay As Date = New Date(2024, 9, 23)
        Dim isHoliday1 As Boolean = AssignmentCalculator.IsOfficialHoliday(nationalDay)
        Console.WriteLine($"اليوم الوطني (23/09): {If(isHoliday1, "عطلة ✓", "ليس عطلة ✗")}")
        
        ' يوم التأسيس
        Dim foundingDay As Date = New Date(2024, 2, 22)
        Dim isHoliday2 As Boolean = AssignmentCalculator.IsOfficialHoliday(foundingDay)
        Console.WriteLine($"يوم التأسيس (22/02): {If(isHoliday2, "عطلة ✓", "ليس عطلة ✗")}")
        
        ' يوم عادي
        Dim normalDay As Date = New Date(2024, 3, 15)
        Dim isHoliday3 As Boolean = AssignmentCalculator.IsOfficialHoliday(normalDay)
        Console.WriteLine($"يوم عادي (15/03): {If(Not isHoliday3, "ليس عطلة ✓", "عطلة ✗")}")
        Console.WriteLine()
    End Sub
    
    ''' <summary>
    ''' اختبار شامل للتكليف
    ''' </summary>
    Public Shared Sub TestCompleteAssignment()
        Console.WriteLine("=== اختبار شامل للتكليف ===")
        
        Dim assignment As New Assignment() With {
            .AssignmentId = "T001",
            .EmployeeId = "001",
            .EmployeeName = "أحمد محمد",
            .StartDate = New Date(2024, 1, 1),
            .EndDate = New Date(2024, 1, 10),
            .DailyAllowance = 300,
            .AssignmentLocation = "الرياض",
            .Status = AssignmentStatus.Approved
        }
        
        Try
            Dim result As AssignmentCalculationResult = AssignmentCalculator.CalculateAssignmentDetails(assignment)
            
            Console.WriteLine($"رقم التكليف: {assignment.AssignmentId}")
            Console.WriteLine($"الموظف: {assignment.EmployeeName}")
            Console.WriteLine($"الفترة: من {assignment.StartDate:dd/MM/yyyy} إلى {assignment.EndDate:dd/MM/yyyy}")
            Console.WriteLine($"إجمالي الأيام: {result.TotalCalendarDays} يوم")
            Console.WriteLine($"أيام العمل: {result.WorkingDays} يوم")
            Console.WriteLine($"الجمع المستثناة: {result.ExcludedFridays} يوم")
            Console.WriteLine($"العطل المستثناة: {result.ExcludedHolidays} يوم")
            Console.WriteLine($"الانتداب اليومي: {result.DailyAllowance:N2} ريال")
            Console.WriteLine($"إجمالي الانتداب: {result.TotalAllowance:N2} ريال")
            Console.WriteLine("الاختبار: نجح ✓")
            
        Catch ex As Exception
            Console.WriteLine($"خطأ في الاختبار: {ex.Message}")
            Console.WriteLine("الاختبار: فشل ✗")
        End Try
        Console.WriteLine()
    End Sub
    
    ''' <summary>
    ''' تشغيل جميع الاختبارات
    ''' </summary>
    Public Shared Sub RunAllTests()
        Console.WriteLine("بدء تشغيل اختبارات النظام...")
        Console.WriteLine("=" * 50)
        Console.WriteLine()
        
        TestAssignmentDaysCalculation()
        TestLeaveDaysCalculation()
        TestDailyAllowanceByGrade()
        TestOfficialHolidays()
        TestCompleteAssignment()
        
        Console.WriteLine("=" * 50)
        Console.WriteLine("انتهاء الاختبارات")
        Console.WriteLine("اضغط أي مفتاح للمتابعة...")
        Console.ReadKey()
    End Sub
    
End Class
