''' <summary>
''' فئة حساب الإجازات - تحتوي على جميع العمليات الحسابية المتعلقة بالإجازات
''' </summary>
Public Class LeaveCalculator
    
    ''' <summary>
    ''' الحد الأقصى لأيام الإجازة حسب النوع
    ''' </summary>
    Private Shared ReadOnly MaxLeaveDaysByType As New Dictionary(Of LeaveType, Integer) From {
        {LeaveType.Annual, 30},
        {LeaveType.Sick, 30},
        {LeaveType.Emergency, 5},
        {LeaveType.Maternity, 70},
        {LeaveType.Paternity, 3},
        {LeaveType.Unpaid, 365}
    }
    
    ''' <summary>
    ''' الرصيد السنوي الافتراضي حسب نوع الإجازة
    ''' </summary>
    Private Shared ReadOnly DefaultAnnualBalance As New Dictionary(Of LeaveType, Integer) From {
        {LeaveType.Annual, 30},
        {LeaveType.Sick, 30},
        {LeaveType.Emergency, 5},
        {LeaveType.Maternity, 70},
        {LeaveType.Paternity, 3},
        {LeaveType.Unpaid, 0}
    }
    
    ''' <summary>
    ''' حساب عدد أيام الإجازة الفعلية (باستثناء عطلة نهاية الأسبوع والعطل الرسمية)
    ''' </summary>
    ''' <param name="startDate">تاريخ بداية الإجازة</param>
    ''' <param name="endDate">تاريخ نهاية الإجازة</param>
    ''' <param name="excludeWeekends">استثناء عطلة نهاية الأسبوع (افتراضي: نعم)</param>
    ''' <param name="excludeHolidays">استثناء العطل الرسمية (افتراضي: نعم)</param>
    ''' <returns>عدد أيام الإجازة الفعلية</returns>
    Public Shared Function CalculateLeaveDays(startDate As Date, endDate As Date, 
                                            Optional excludeWeekends As Boolean = True, 
                                            Optional excludeHolidays As Boolean = True) As Integer
        
        If startDate > endDate Then
            Throw New ArgumentException("تاريخ البداية يجب أن يكون قبل تاريخ النهاية")
        End If
        
        Dim totalDays As Integer = 0
        Dim currentDate As Date = startDate
        
        While currentDate <= endDate
            Dim includeDay As Boolean = True
            
            ' فحص عطلة نهاية الأسبوع (الجمعة والسبت)
            If excludeWeekends AndAlso (currentDate.DayOfWeek = DayOfWeek.Friday OrElse currentDate.DayOfWeek = DayOfWeek.Saturday) Then
                includeDay = False
            End If
            
            ' فحص العطل الرسمية
            If includeDay AndAlso excludeHolidays AndAlso AssignmentCalculator.IsOfficialHoliday(currentDate) Then
                includeDay = False
            End If
            
            If includeDay Then
                totalDays += 1
            End If
            
            currentDate = currentDate.AddDays(1)
        End While
        
        Return totalDays
    End Function
    
    ''' <summary>
    ''' التحقق من توفر رصيد كافي للإجازة
    ''' </summary>
    ''' <param name="employee">بيانات الموظف</param>
    ''' <param name="leaveType">نوع الإجازة</param>
    ''' <param name="requestedDays">عدد الأيام المطلوبة</param>
    ''' <returns>صحيح إذا كان الرصيد كافي</returns>
    Public Shared Function HasSufficientBalance(employee As Employee, leaveType As LeaveType, requestedDays As Integer) As Boolean
        If employee Is Nothing Then
            Return False
        End If
        
        Dim currentBalance As Integer = GetCurrentBalance(employee, leaveType)
        Return currentBalance >= requestedDays
    End Function
    
    ''' <summary>
    ''' الحصول على الرصيد الحالي للموظف حسب نوع الإجازة
    ''' </summary>
    ''' <param name="employee">بيانات الموظف</param>
    ''' <param name="leaveType">نوع الإجازة</param>
    ''' <returns>الرصيد الحالي</returns>
    Public Shared Function GetCurrentBalance(employee As Employee, leaveType As LeaveType) As Integer
        If employee Is Nothing Then
            Return 0
        End If
        
        Select Case leaveType
            Case LeaveType.Annual
                Return employee.AnnualLeaveBalance
            Case LeaveType.Sick
                Return employee.SickLeaveBalance
            Case LeaveType.Emergency
                Return employee.EmergencyLeaveBalance
            Case LeaveType.Maternity, LeaveType.Paternity
                Return DefaultAnnualBalance(leaveType)
            Case LeaveType.Unpaid
                Return Integer.MaxValue ' لا يوجد حد للإجازة بدون راتب
            Case Else
                Return 0
        End Select
    End Function
    
    ''' <summary>
    ''' تحديث رصيد الإجازة بعد الموافقة
    ''' </summary>
    ''' <param name="employee">بيانات الموظف</param>
    ''' <param name="leaveType">نوع الإجازة</param>
    ''' <param name="usedDays">عدد الأيام المستخدمة</param>
    Public Shared Sub UpdateLeaveBalance(employee As Employee, leaveType As LeaveType, usedDays As Integer)
        If employee Is Nothing OrElse usedDays <= 0 Then
            Return
        End If
        
        Select Case leaveType
            Case LeaveType.Annual
                employee.AnnualLeaveBalance = Math.Max(0, employee.AnnualLeaveBalance - usedDays)
            Case LeaveType.Sick
                employee.SickLeaveBalance = Math.Max(0, employee.SickLeaveBalance - usedDays)
            Case LeaveType.Emergency
                employee.EmergencyLeaveBalance = Math.Max(0, employee.EmergencyLeaveBalance - usedDays)
            ' إجازة الأمومة والأبوة والإجازة بدون راتب لا تؤثر على الأرصدة الأخرى
        End Select
    End Sub
    
    ''' <summary>
    ''' حساب تفاصيل الإجازة الكاملة
    ''' </summary>
    ''' <param name="leave">بيانات الإجازة</param>
    ''' <param name="employee">بيانات الموظف</param>
    ''' <returns>تفاصيل حساب الإجازة</returns>
    Public Shared Function CalculateLeaveDetails(leave As Leave, employee As Employee) As LeaveCalculationResult
        If leave Is Nothing Then
            Throw New ArgumentNullException("leave", "بيانات الإجازة مطلوبة")
        End If
        
        If employee Is Nothing Then
            Throw New ArgumentNullException("employee", "بيانات الموظف مطلوبة")
        End If
        
        If Not leave.IsValid() Then
            Throw New ArgumentException("بيانات الإجازة غير صحيحة")
        End If
        
        Dim result As New LeaveCalculationResult()
        
        ' حساب الأيام
        result.TotalCalendarDays = CInt((leave.EndDate - leave.StartDate).TotalDays) + 1
        result.WorkingDays = CalculateLeaveDays(leave.StartDate, leave.EndDate, True, True)
        result.ExcludedWeekends = CalculateWeekendsInPeriod(leave.StartDate, leave.EndDate)
        result.ExcludedHolidays = CalculateHolidaysInPeriod(leave.StartDate, leave.EndDate)
        
        ' معلومات الرصيد
        result.CurrentBalance = GetCurrentBalance(employee, leave.LeaveType)
        result.BalanceAfterLeave = Math.Max(0, result.CurrentBalance - result.WorkingDays)
        result.HasSufficientBalance = HasSufficientBalance(employee, leave.LeaveType, result.WorkingDays)
        
        ' معلومات إضافية
        result.StartDate = leave.StartDate
        result.EndDate = leave.EndDate
        result.LeaveType = leave.LeaveType
        result.LeaveTypeName = leave.GetLeaveTypeText()
        result.IsPaid = leave.IsPaid
        
        ' حساب الخصم من الراتب (إذا كانت الإجازة بدون راتب أو تجاوز الرصيد)
        If leave.LeaveType = LeaveType.Unpaid OrElse Not result.HasSufficientBalance Then
            result.UnpaidDays = If(leave.LeaveType = LeaveType.Unpaid, result.WorkingDays, 
                                 Math.Max(0, result.WorkingDays - result.CurrentBalance))
            result.SalaryDeduction = CalculateSalaryDeduction(employee, result.UnpaidDays)
        End If
        
        Return result
    End Function
    
    ''' <summary>
    ''' حساب خصم الراتب للأيام بدون راتب
    ''' </summary>
    ''' <param name="employee">بيانات الموظف</param>
    ''' <param name="unpaidDays">عدد الأيام بدون راتب</param>
    ''' <returns>مبلغ الخصم</returns>
    Private Shared Function CalculateSalaryDeduction(employee As Employee, unpaidDays As Integer) As Decimal
        If employee Is Nothing OrElse unpaidDays <= 0 Then
            Return 0
        End If
        
        ' حساب الراتب اليومي (الراتب الشهري ÷ 30)
        Dim dailySalary As Decimal = employee.BasicSalary / 30
        Return dailySalary * unpaidDays
    End Function
    
    ''' <summary>
    ''' حساب عدد عطل نهاية الأسبوع في فترة معينة
    ''' </summary>
    Private Shared Function CalculateWeekendsInPeriod(startDate As Date, endDate As Date) As Integer
        Dim weekendCount As Integer = 0
        Dim currentDate As Date = startDate
        
        While currentDate <= endDate
            If currentDate.DayOfWeek = DayOfWeek.Friday OrElse currentDate.DayOfWeek = DayOfWeek.Saturday Then
                weekendCount += 1
            End If
            currentDate = currentDate.AddDays(1)
        End While
        
        Return weekendCount
    End Function
    
    ''' <summary>
    ''' حساب عدد العطل الرسمية في فترة معينة
    ''' </summary>
    Private Shared Function CalculateHolidaysInPeriod(startDate As Date, endDate As Date) As Integer
        Dim holidayCount As Integer = 0
        Dim currentDate As Date = startDate
        
        While currentDate <= endDate
            If AssignmentCalculator.IsOfficialHoliday(currentDate) Then
                holidayCount += 1
            End If
            currentDate = currentDate.AddDays(1)
        End While
        
        Return holidayCount
    End Function
    
    ''' <summary>
    ''' الحصول على الحد الأقصى لأيام الإجازة حسب النوع
    ''' </summary>
    ''' <param name="leaveType">نوع الإجازة</param>
    ''' <returns>الحد الأقصى للأيام</returns>
    Public Shared Function GetMaxLeaveDays(leaveType As LeaveType) As Integer
        If MaxLeaveDaysByType.ContainsKey(leaveType) Then
            Return MaxLeaveDaysByType(leaveType)
        Else
            Return 30 ' قيمة افتراضية
        End If
    End Function
    
    ''' <summary>
    ''' إعادة تعيين الأرصدة السنوية للموظف
    ''' </summary>
    ''' <param name="employee">بيانات الموظف</param>
    Public Shared Sub ResetAnnualBalances(employee As Employee)
        If employee Is Nothing Then
            Return
        End If
        
        employee.AnnualLeaveBalance = DefaultAnnualBalance(LeaveType.Annual)
        employee.SickLeaveBalance = DefaultAnnualBalance(LeaveType.Sick)
        employee.EmergencyLeaveBalance = DefaultAnnualBalance(LeaveType.Emergency)
    End Sub
    
End Class

''' <summary>
''' نتيجة حساب الإجازة
''' </summary>
Public Class LeaveCalculationResult
    Public Property StartDate As Date
    Public Property EndDate As Date
    Public Property LeaveType As LeaveType
    Public Property LeaveTypeName As String
    Public Property TotalCalendarDays As Integer
    Public Property WorkingDays As Integer
    Public Property ExcludedWeekends As Integer
    Public Property ExcludedHolidays As Integer
    Public Property CurrentBalance As Integer
    Public Property BalanceAfterLeave As Integer
    Public Property HasSufficientBalance As Boolean
    Public Property IsPaid As Boolean
    Public Property UnpaidDays As Integer
    Public Property SalaryDeduction As Decimal
    
    Public Overrides Function ToString() As String
        Return $"{LeaveTypeName}: {WorkingDays} أيام - الرصيد المتبقي: {BalanceAfterLeave}"
    End Function
End Class
