Imports System
Imports System.Windows.Forms

Module SimpleTest
    <STAThread>
    Sub Main()
        Try
            Console.WriteLine("Starting application...")
            
            Application.EnableVisualStyles()
            Application.SetCompatibleTextRenderingDefault(False)
            
            Console.WriteLine("Creating form...")
            Dim form As New Form()
            form.Text = "اختبار البرنامج"
            form.Size = New System.Drawing.Size(400, 300)
            form.StartPosition = FormStartPosition.CenterScreen
            
            Dim label As New Label()
            label.Text = "البرنامج يعمل بنجاح!"
            label.Location = New System.Drawing.Point(50, 50)
            label.Size = New System.Drawing.Size(300, 30)
            label.Font = New System.Drawing.Font("Tahoma", 12)
            form.Controls.Add(label)
            
            Console.WriteLine("Running application...")
            Application.Run(form)
            
        Catch ex As Exception
            Console.WriteLine($"Error: {ex.Message}")
            Console.WriteLine($"Stack Trace: {ex.StackTrace}")
            If ex.InnerException IsNot Nothing Then
                Console.WriteLine($"Inner Exception: {ex.InnerException.Message}")
            End If
            Console.WriteLine("Press any key to exit...")
            Console.ReadKey()
        End Try
    End Sub
End Module
