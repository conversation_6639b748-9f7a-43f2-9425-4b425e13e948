Imports System.Globalization

''' <summary>
''' فئة حساب التكليفات - تحتوي على جميع العمليات الحسابية المتعلقة بالتكليفات
''' </summary>
Public Class AssignmentCalculator
    
    ''' <summary>
    ''' قائمة العطل الرسمية الثابتة في المملكة العربية السعودية
    ''' </summary>
    Private Shared ReadOnly OfficialHolidays As New Dictionary(Of String, String) From {
        {"09-23", "اليوم الوطني السعودي"},
        {"02-22", "يوم التأسيس"},
        {"05-01", "عيد العمال"}
    }
    
    ''' <summary>
    ''' قيم الانتداب اليومي حسب المرتبة الوظيفية
    ''' </summary>
    Private Shared ReadOnly DailyAllowanceByGrade As New Dictionary(Of String, Decimal) From {
        {"الأولى", 500D},
        {"الثانية", 450D},
        {"الثالثة", 400D},
        {"الرابعة", 350D},
        {"الخامسة", 300D},
        {"السادسة", 250D},
        {"السابعة", 200D},
        {"الثامنة", 180D},
        {"التاسعة", 160D},
        {"العاشرة", 150D}
    }
    
    ''' <summary>
    ''' حساب عدد أيام التكليف الفعلية (باستثناء الجمع والعطل الرسمية)
    ''' </summary>
    ''' <param name="startDate">تاريخ بداية التكليف</param>
    ''' <param name="endDate">تاريخ نهاية التكليف</param>
    ''' <param name="excludeFridays">استثناء أيام الجمعة (افتراضي: نعم)</param>
    ''' <param name="excludeHolidays">استثناء العطل الرسمية (افتراضي: نعم)</param>
    ''' <returns>عدد الأيام الفعلية للتكليف</returns>
    Public Shared Function CalculateAssignmentDays(startDate As Date, endDate As Date, 
                                                 Optional excludeFridays As Boolean = True, 
                                                 Optional excludeHolidays As Boolean = True) As Integer
        
        If startDate > endDate Then
            Throw New ArgumentException("تاريخ البداية يجب أن يكون قبل تاريخ النهاية")
        End If
        
        Dim totalDays As Integer = 0
        Dim currentDate As Date = startDate
        
        While currentDate <= endDate
            Dim includeDay As Boolean = True
            
            ' فحص يوم الجمعة
            If excludeFridays AndAlso currentDate.DayOfWeek = DayOfWeek.Friday Then
                includeDay = False
            End If
            
            ' فحص العطل الرسمية
            If includeDay AndAlso excludeHolidays AndAlso IsOfficialHoliday(currentDate) Then
                includeDay = False
            End If
            
            If includeDay Then
                totalDays += 1
            End If
            
            currentDate = currentDate.AddDays(1)
        End While
        
        Return totalDays
    End Function
    
    ''' <summary>
    ''' حساب إجمالي قيمة الانتداب
    ''' </summary>
    ''' <param name="totalDays">عدد أيام التكليف</param>
    ''' <param name="dailyAllowance">قيمة الانتداب اليومي</param>
    ''' <returns>إجمالي قيمة الانتداب</returns>
    Public Shared Function CalculateTotalAllowance(totalDays As Integer, dailyAllowance As Decimal) As Decimal
        If totalDays < 0 Then
            Throw New ArgumentException("عدد الأيام يجب أن يكون أكبر من أو يساوي صفر")
        End If
        
        If dailyAllowance < 0 Then
            Throw New ArgumentException("قيمة الانتداب اليومي يجب أن تكون أكبر من أو تساوي صفر")
        End If
        
        Return totalDays * dailyAllowance
    End Function
    
    ''' <summary>
    ''' الحصول على قيمة الانتداب اليومي حسب المرتبة
    ''' </summary>
    ''' <param name="jobGrade">المرتبة الوظيفية</param>
    ''' <returns>قيمة الانتداب اليومي</returns>
    Public Shared Function GetDailyAllowanceByGrade(jobGrade As String) As Decimal
        If String.IsNullOrWhiteSpace(jobGrade) Then
            Return 200D ' قيمة افتراضية
        End If
        
        If DailyAllowanceByGrade.ContainsKey(jobGrade) Then
            Return DailyAllowanceByGrade(jobGrade)
        Else
            Return 200D ' قيمة افتراضية للمراتب غير المحددة
        End If
    End Function
    
    ''' <summary>
    ''' فحص إذا كان التاريخ عطلة رسمية
    ''' </summary>
    ''' <param name="checkDate">التاريخ المراد فحصه</param>
    ''' <returns>صحيح إذا كان عطلة رسمية</returns>
    Public Shared Function IsOfficialHoliday(checkDate As Date) As Boolean
        Dim dateKey As String = checkDate.ToString("MM-dd")
        Return OfficialHolidays.ContainsKey(dateKey)
    End Function
    
    ''' <summary>
    ''' الحصول على اسم العطلة الرسمية
    ''' </summary>
    ''' <param name="checkDate">التاريخ المراد فحصه</param>
    ''' <returns>اسم العطلة أو نص فارغ</returns>
    Public Shared Function GetHolidayName(checkDate As Date) As String
        Dim dateKey As String = checkDate.ToString("MM-dd")
        If OfficialHolidays.ContainsKey(dateKey) Then
            Return OfficialHolidays(dateKey)
        Else
            Return String.Empty
        End If
    End Function
    
    ''' <summary>
    ''' حساب تفاصيل التكليف الكاملة
    ''' </summary>
    ''' <param name="assignment">بيانات التكليف</param>
    ''' <returns>تفاصيل الحساب</returns>
    Public Shared Function CalculateAssignmentDetails(assignment As Assignment) As AssignmentCalculationResult
        If assignment Is Nothing Then
            Throw New ArgumentNullException("assignment", "بيانات التكليف مطلوبة")
        End If
        
        If Not assignment.IsValid() Then
            Throw New ArgumentException("بيانات التكليف غير صحيحة")
        End If
        
        Dim result As New AssignmentCalculationResult()
        
        ' حساب الأيام
        result.TotalCalendarDays = CInt((assignment.EndDate - assignment.StartDate).TotalDays) + 1
        result.WorkingDays = CalculateAssignmentDays(assignment.StartDate, assignment.EndDate, True, True)
        result.ExcludedFridays = CalculateAssignmentDays(assignment.StartDate, assignment.EndDate, False, True) - result.WorkingDays
        result.ExcludedHolidays = CalculateHolidaysInPeriod(assignment.StartDate, assignment.EndDate)
        
        ' حساب المبالغ
        result.DailyAllowance = assignment.DailyAllowance
        result.TotalAllowance = CalculateTotalAllowance(result.WorkingDays, result.DailyAllowance)
        
        ' معلومات إضافية
        result.StartDate = assignment.StartDate
        result.EndDate = assignment.EndDate
        result.AssignmentLocation = assignment.AssignmentLocation
        
        Return result
    End Function
    
    ''' <summary>
    ''' حساب عدد العطل الرسمية في فترة معينة
    ''' </summary>
    Private Shared Function CalculateHolidaysInPeriod(startDate As Date, endDate As Date) As Integer
        Dim holidayCount As Integer = 0
        Dim currentDate As Date = startDate
        
        While currentDate <= endDate
            If IsOfficialHoliday(currentDate) Then
                holidayCount += 1
            End If
            currentDate = currentDate.AddDays(1)
        End While
        
        Return holidayCount
    End Function
    
    ''' <summary>
    ''' الحصول على قائمة جميع المراتب المتاحة
    ''' </summary>
    ''' <returns>قائمة المراتب</returns>
    Public Shared Function GetAvailableGrades() As List(Of String)
        Return DailyAllowanceByGrade.Keys.ToList()
    End Function
    
    ''' <summary>
    ''' تحديث قيمة الانتداب لمرتبة معينة
    ''' </summary>
    ''' <param name="grade">المرتبة</param>
    ''' <param name="allowance">قيمة الانتداب الجديدة</param>
    Public Shared Sub UpdateDailyAllowance(grade As String, allowance As Decimal)
        If Not String.IsNullOrWhiteSpace(grade) AndAlso allowance >= 0 Then
            DailyAllowanceByGrade(grade) = allowance
        End If
    End Sub
    
End Class

''' <summary>
''' نتيجة حساب التكليف
''' </summary>
Public Class AssignmentCalculationResult
    Public Property StartDate As Date
    Public Property EndDate As Date
    Public Property AssignmentLocation As String
    Public Property TotalCalendarDays As Integer
    Public Property WorkingDays As Integer
    Public Property ExcludedFridays As Integer
    Public Property ExcludedHolidays As Integer
    Public Property DailyAllowance As Decimal
    Public Property TotalAllowance As Decimal
    
    Public Overrides Function ToString() As String
        Return $"أيام العمل: {WorkingDays} - إجمالي الانتداب: {TotalAllowance:C} ريال"
    End Function
End Class
