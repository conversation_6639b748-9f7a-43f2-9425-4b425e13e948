Imports System.Windows.Forms
Imports System.Drawing

Public Class SimpleMainForm
    Inherits Form
    
    Private WithEvents btnTest As Button
    Private lblWelcome As Label
    
    Public Sub New()
        InitializeComponent()
    End Sub
    
    Private Sub InitializeComponent()
        Try
            ' إعدادات النموذج
            Me.Text = "نظام حسابات الموظفين - اختبار"
            Me.Size = New Size(800, 600)
            Me.StartPosition = FormStartPosition.CenterScreen
            Me.Font = New Font("Tahoma", 10)
            Me.RightToLeft = RightToLeft.Yes
            Me.TopMost = True
            
            ' تسمية ترحيبية
            lblWelcome = New Label()
            lblWelcome.Text = "مرحباً بك في نظام حسابات الموظفين" & vbCrLf & "البرنامج يعمل بنجاح!"
            lblWelcome.Location = New Point(200, 100)
            lblWelcome.Size = New Size(400, 100)
            lblWelcome.Font = New Font("Tahoma", 14, FontStyle.Bold)
            lblWelcome.TextAlign = ContentAlignment.MiddleCenter
            lblWelcome.BackColor = Color.LightBlue
            Me.Controls.Add(lblWelcome)
            
            ' زر اختبار
            btnTest = New Button()
            btnTest.Text = "اختبار البرنامج"
            btnTest.Location = New Point(300, 250)
            btnTest.Size = New Size(200, 50)
            btnTest.Font = New Font("Tahoma", 12)
            btnTest.BackColor = Color.LightGreen
            Me.Controls.Add(btnTest)
            
        Catch ex As Exception
            MessageBox.Show($"خطأ في تهيئة النموذج: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub btnTest_Click(sender As Object, e As EventArgs) Handles btnTest.Click
        Try
            MessageBox.Show("البرنامج يعمل بنجاح!" & vbCrLf & "جميع المكونات تعمل بشكل صحيح", "نجح الاختبار", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            MessageBox.Show($"خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
End Class
