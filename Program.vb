Imports System
Imports System.Windows.Forms
Imports System.Threading
Imports System.Globalization

''' <summary>
''' نقطة الدخول الرئيسية لبرنامج حسابات الموظفين
''' </summary>
Module Program

    ''' <summary>
    ''' الدالة الرئيسية لتشغيل التطبيق
    ''' </summary>
    <STAThread>
    Sub Main()
        Try
            ' تعيين الثقافة العربية
            Thread.CurrentThread.CurrentCulture = New CultureInfo("ar-SA")
            Thread.CurrentThread.CurrentUICulture = New CultureInfo("ar-SA")

            ' تهيئة إعدادات Windows Forms
            Application.EnableVisualStyles()
            Application.SetCompatibleTextRenderingDefault(False)
            Application.SetHighDpiMode(HighDpiMode.SystemAware)

            ' إنشاء وتشغيل النموذج الرئيسي
            Using mainForm As New MainForm()
                Application.Run(mainForm)
            End Using

        Catch ex As Exception
            ' معالجة الأخطاء العامة
            Dim errorMessage As String = $"حدث خطأ غير متوقع في التطبيق:{vbCrLf}{vbCrLf}{ex.Message}"

            If ex.InnerException IsNot Nothing Then
                errorMessage &= $"{vbCrLf}{vbCrLf}تفاصيل إضافية: {ex.InnerException.Message}"
            End If

            MessageBox.Show(errorMessage, "خطأ في التطبيق", MessageBoxButtons.OK, MessageBoxIcon.Error)

            ' تسجيل الخطأ في ملف (اختياري)
            LogError(ex)
        End Try
    End Sub

    ''' <summary>
    ''' تسجيل الأخطاء في ملف نصي
    ''' </summary>
    ''' <param name="ex">الاستثناء المراد تسجيله</param>
    Private Sub LogError(ex As Exception)
        Try
            Dim logPath As String = IO.Path.Combine(Application.StartupPath, "error_log.txt")
            Dim logEntry As String = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {ex.Message}{vbCrLf}{ex.StackTrace}{vbCrLf}{New String("="c, 50)}{vbCrLf}"
            IO.File.AppendAllText(logPath, logEntry)
        Catch
            ' تجاهل أخطاء التسجيل
        End Try
    End Sub

End Module
