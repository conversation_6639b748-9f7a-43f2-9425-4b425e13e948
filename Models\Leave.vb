''' <summary>
''' فئة الإجازة - تحتوي على بيانات إجازات الموظفين
''' </summary>
Public Class Leave
    
    ''' <summary>
    ''' رقم الإجازة (مفتاح أساسي)
    ''' </summary>
    Public Property LeaveId As String
    
    ''' <summary>
    ''' رقم الموظف
    ''' </summary>
    Public Property EmployeeId As String
    
    ''' <summary>
    ''' اسم الموظف
    ''' </summary>
    Public Property EmployeeName As String
    
    ''' <summary>
    ''' نوع الإجازة
    ''' </summary>
    Public Property LeaveType As LeaveType
    
    ''' <summary>
    ''' تاريخ بداية الإجازة
    ''' </summary>
    Public Property StartDate As Date
    
    ''' <summary>
    ''' تاريخ نهاية الإجازة
    ''' </summary>
    Public Property EndDate As Date
    
    ''' <summary>
    ''' عدد أيام الإجازة (محسوب)
    ''' </summary>
    Public ReadOnly Property TotalDays As Integer
        Get
            Return CalculateTotalDays()
        End Get
    End Property
    
    ''' <summary>
    ''' سبب الإجازة
    ''' </summary>
    Public Property Reason As String
    
    ''' <summary>
    ''' حالة الإجازة
    ''' </summary>
    Public Property Status As LeaveStatus
    
    ''' <summary>
    ''' تاريخ تقديم طلب الإجازة
    ''' </summary>
    Public Property ApplicationDate As Date
    
    ''' <summary>
    ''' تاريخ الموافقة على الإجازة
    ''' </summary>
    Public Property ApprovalDate As Date?
    
    ''' <summary>
    ''' الموظف الذي وافق على الإجازة
    ''' </summary>
    Public Property ApprovedBy As String
    
    ''' <summary>
    ''' ملاحظات إضافية
    ''' </summary>
    Public Property Notes As String
    
    ''' <summary>
    ''' هل الإجازة مدفوعة الأجر
    ''' </summary>
    Public Property IsPaid As Boolean
    
    ''' <summary>
    ''' منشئ افتراضي
    ''' </summary>
    Public Sub New()
        Me.ApplicationDate = Date.Now
        Me.Status = LeaveStatus.Pending
        Me.IsPaid = True
    End Sub
    
    ''' <summary>
    ''' منشئ مع المعاملات الأساسية
    ''' </summary>
    Public Sub New(leaveId As String, employeeId As String, leaveType As LeaveType, startDate As Date, endDate As Date)
        Me.New()
        Me.LeaveId = leaveId
        Me.EmployeeId = employeeId
        Me.LeaveType = leaveType
        Me.StartDate = startDate
        Me.EndDate = endDate
    End Sub
    
    ''' <summary>
    ''' حساب عدد أيام الإجازة (باستثناء أيام الجمعة والسبت والعطل الرسمية)
    ''' </summary>
    Private Function CalculateTotalDays() As Integer
        If StartDate > EndDate Then
            Return 0
        End If
        
        Dim totalDays As Integer = 0
        Dim currentDate As Date = StartDate
        
        While currentDate <= EndDate
            ' استثناء عطلة نهاية الأسبوع (الجمعة والسبت)
            If currentDate.DayOfWeek <> DayOfWeek.Friday AndAlso currentDate.DayOfWeek <> DayOfWeek.Saturday Then
                ' استثناء العطل الرسمية
                If Not IsOfficialHoliday(currentDate) Then
                    totalDays += 1
                End If
            End If
            currentDate = currentDate.AddDays(1)
        End While
        
        Return totalDays
    End Function
    
    ''' <summary>
    ''' فحص إذا كان التاريخ عطلة رسمية
    ''' </summary>
    Private Function IsOfficialHoliday(checkDate As Date) As Boolean
        ' قائمة العطل الرسمية في المملكة العربية السعودية
        Dim holidays As New List(Of Date) From {
            New Date(checkDate.Year, 9, 23), ' اليوم الوطني
            New Date(checkDate.Year, 2, 22)  ' يوم التأسيس
        }
        
        Return holidays.Contains(checkDate.Date)
    End Function
    
    ''' <summary>
    ''' التحقق من صحة بيانات الإجازة
    ''' </summary>
    Public Function IsValid() As Boolean
        Return Not String.IsNullOrWhiteSpace(LeaveId) AndAlso
               Not String.IsNullOrWhiteSpace(EmployeeId) AndAlso
               StartDate <= EndDate AndAlso
               ApplicationDate <= StartDate
    End Function
    
    ''' <summary>
    ''' إرجاع تمثيل نصي للإجازة
    ''' </summary>
    Public Overrides Function ToString() As String
        Return $"{LeaveId} - {EmployeeName} ({GetLeaveTypeText()}) - {TotalDays} أيام"
    End Function
    
    ''' <summary>
    ''' إرجاع النص المقابل لنوع الإجازة
    ''' </summary>
    Public Function GetLeaveTypeText() As String
        Select Case LeaveType
            Case LeaveType.Annual
                Return "إجازة اعتيادية"
            Case LeaveType.Sick
                Return "إجازة مرضية"
            Case LeaveType.Emergency
                Return "إجازة اضطرارية"
            Case LeaveType.Maternity
                Return "إجازة أمومة"
            Case LeaveType.Paternity
                Return "إجازة أبوة"
            Case LeaveType.Unpaid
                Return "إجازة بدون راتب"
            Case Else
                Return "غير محدد"
        End Select
    End Function
    
End Class

''' <summary>
''' أنواع الإجازات
''' </summary>
Public Enum LeaveType
    ''' <summary>
    ''' إجازة اعتيادية
    ''' </summary>
    Annual = 0
    
    ''' <summary>
    ''' إجازة مرضية
    ''' </summary>
    Sick = 1
    
    ''' <summary>
    ''' إجازة اضطرارية
    ''' </summary>
    Emergency = 2
    
    ''' <summary>
    ''' إجازة أمومة
    ''' </summary>
    Maternity = 3
    
    ''' <summary>
    ''' إجازة أبوة
    ''' </summary>
    Paternity = 4
    
    ''' <summary>
    ''' إجازة بدون راتب
    ''' </summary>
    Unpaid = 5
End Enum

''' <summary>
''' حالات الإجازة
''' </summary>
Public Enum LeaveStatus
    ''' <summary>
    ''' في الانتظار
    ''' </summary>
    Pending = 0
    
    ''' <summary>
    ''' موافق عليها
    ''' </summary>
    Approved = 1
    
    ''' <summary>
    ''' مرفوضة
    ''' </summary>
    Rejected = 2
    
    ''' <summary>
    ''' ملغاة
    ''' </summary>
    Cancelled = 3
    
    ''' <summary>
    ''' مكتملة
    ''' </summary>
    Completed = 4
End Enum
