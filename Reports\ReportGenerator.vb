Imports System.IO
Imports System.Text

''' <summary>
''' فئة إنتاج التقارير - تحتوي على وظائف إنتاج التقارير بصيغ مختلفة
''' </summary>
Public Class ReportGenerator
    
    ''' <summary>
    ''' إنتاج تقرير التكليفات بصيغة HTML
    ''' </summary>
    ''' <param name="assignments">قائمة التكليفات</param>
    ''' <param name="employees">قائمة الموظفين</param>
    ''' <returns>محتوى HTML للتقرير</returns>
    Public Shared Function GenerateAssignmentReport(assignments As List(Of Assignment), employees As List(Of Employee)) As String
        Dim html As New StringBuilder()
        
        html.AppendLine("<!DOCTYPE html>")
        html.AppendLine("<html dir='rtl' lang='ar'>")
        html.AppendLine("<head>")
        html.AppendLine("<meta charset='UTF-8'>")
        html.AppendLine("<title>تقرير التكليفات</title>")
        html.AppendLine("<style>")
        html.AppendLine("body { font-family: 'Tahoma', Arial, sans-serif; direction: rtl; }")
        html.AppendLine("table { width: 100%; border-collapse: collapse; margin: 20px 0; }")
        html.AppendLine("th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }")
        html.AppendLine("th { background-color: #f2f2f2; font-weight: bold; }")
        html.AppendLine("h1 { text-align: center; color: #333; }")
        html.AppendLine(".header { text-align: center; margin-bottom: 30px; }")
        html.AppendLine(".summary { background-color: #f9f9f9; padding: 15px; margin: 20px 0; }")
        html.AppendLine("</style>")
        html.AppendLine("</head>")
        html.AppendLine("<body>")
        
        html.AppendLine("<div class='header'>")
        html.AppendLine("<h1>تقرير التكليفات الرسمية</h1>")
        html.AppendLine($"<p>تاريخ التقرير: {Date.Now:dd/MM/yyyy}</p>")
        html.AppendLine("</div>")
        
        ' إحصائيات عامة
        html.AppendLine("<div class='summary'>")
        html.AppendLine("<h2>الإحصائيات العامة</h2>")
        html.AppendLine($"<p>إجمالي عدد التكليفات: {assignments.Count}</p>")
        
        Dim totalAllowance As Decimal = assignments.Sum(Function(a) a.TotalAllowance)
        html.AppendLine($"<p>إجمالي قيمة الانتداب: {totalAllowance:N2} ريال</p>")
        
        Dim totalDays As Integer = assignments.Sum(Function(a) a.TotalDays)
        html.AppendLine($"<p>إجمالي أيام التكليف: {totalDays} يوم</p>")
        html.AppendLine("</div>")
        
        ' جدول التكليفات
        html.AppendLine("<table>")
        html.AppendLine("<thead>")
        html.AppendLine("<tr>")
        html.AppendLine("<th>رقم التكليف</th>")
        html.AppendLine("<th>اسم الموظف</th>")
        html.AppendLine("<th>تاريخ البداية</th>")
        html.AppendLine("<th>تاريخ النهاية</th>")
        html.AppendLine("<th>عدد الأيام</th>")
        html.AppendLine("<th>مكان التكليف</th>")
        html.AppendLine("<th>الانتداب اليومي</th>")
        html.AppendLine("<th>إجمالي الانتداب</th>")
        html.AppendLine("<th>الحالة</th>")
        html.AppendLine("</tr>")
        html.AppendLine("</thead>")
        html.AppendLine("<tbody>")
        
        For Each assignment In assignments
            html.AppendLine("<tr>")
            html.AppendLine($"<td>{assignment.AssignmentId}</td>")
            html.AppendLine($"<td>{assignment.EmployeeName}</td>")
            html.AppendLine($"<td>{assignment.StartDate:dd/MM/yyyy}</td>")
            html.AppendLine($"<td>{assignment.EndDate:dd/MM/yyyy}</td>")
            html.AppendLine($"<td>{assignment.TotalDays}</td>")
            html.AppendLine($"<td>{assignment.AssignmentLocation}</td>")
            html.AppendLine($"<td>{assignment.DailyAllowance:N2}</td>")
            html.AppendLine($"<td>{assignment.TotalAllowance:N2}</td>")
            html.AppendLine($"<td>{GetAssignmentStatusText(assignment.Status)}</td>")
            html.AppendLine("</tr>")
        Next
        
        html.AppendLine("</tbody>")
        html.AppendLine("</table>")
        html.AppendLine("</body>")
        html.AppendLine("</html>")
        
        Return html.ToString()
    End Function
    
    ''' <summary>
    ''' إنتاج تقرير الإجازات بصيغة HTML
    ''' </summary>
    ''' <param name="leaves">قائمة الإجازات</param>
    ''' <param name="employees">قائمة الموظفين</param>
    ''' <returns>محتوى HTML للتقرير</returns>
    Public Shared Function GenerateLeaveReport(leaves As List(Of Leave), employees As List(Of Employee)) As String
        Dim html As New StringBuilder()
        
        html.AppendLine("<!DOCTYPE html>")
        html.AppendLine("<html dir='rtl' lang='ar'>")
        html.AppendLine("<head>")
        html.AppendLine("<meta charset='UTF-8'>")
        html.AppendLine("<title>تقرير الإجازات</title>")
        html.AppendLine("<style>")
        html.AppendLine("body { font-family: 'Tahoma', Arial, sans-serif; direction: rtl; }")
        html.AppendLine("table { width: 100%; border-collapse: collapse; margin: 20px 0; }")
        html.AppendLine("th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }")
        html.AppendLine("th { background-color: #f2f2f2; font-weight: bold; }")
        html.AppendLine("h1 { text-align: center; color: #333; }")
        html.AppendLine(".header { text-align: center; margin-bottom: 30px; }")
        html.AppendLine(".summary { background-color: #f9f9f9; padding: 15px; margin: 20px 0; }")
        html.AppendLine("</style>")
        html.AppendLine("</head>")
        html.AppendLine("<body>")
        
        html.AppendLine("<div class='header'>")
        html.AppendLine("<h1>تقرير الإجازات</h1>")
        html.AppendLine($"<p>تاريخ التقرير: {Date.Now:dd/MM/yyyy}</p>")
        html.AppendLine("</div>")
        
        ' إحصائيات عامة
        html.AppendLine("<div class='summary'>")
        html.AppendLine("<h2>الإحصائيات العامة</h2>")
        html.AppendLine($"<p>إجمالي عدد الإجازات: {leaves.Count}</p>")
        
        Dim totalDays As Integer = leaves.Sum(Function(l) l.TotalDays)
        html.AppendLine($"<p>إجمالي أيام الإجازات: {totalDays} يوم</p>")
        
        ' إحصائيات حسب النوع
        For Each leaveType As LeaveType In [Enum].GetValues(GetType(LeaveType))
            Dim typeCount = leaves.Where(Function(l) l.LeaveType = leaveType).Count()
            If typeCount > 0 Then
                Dim typeDays = leaves.Where(Function(l) l.LeaveType = leaveType).Sum(Function(l) l.TotalDays)
                html.AppendLine($"<p>{GetLeaveTypeText(leaveType)}: {typeCount} إجازة ({typeDays} يوم)</p>")
            End If
        Next
        html.AppendLine("</div>")
        
        ' جدول الإجازات
        html.AppendLine("<table>")
        html.AppendLine("<thead>")
        html.AppendLine("<tr>")
        html.AppendLine("<th>رقم الإجازة</th>")
        html.AppendLine("<th>اسم الموظف</th>")
        html.AppendLine("<th>نوع الإجازة</th>")
        html.AppendLine("<th>تاريخ البداية</th>")
        html.AppendLine("<th>تاريخ النهاية</th>")
        html.AppendLine("<th>عدد الأيام</th>")
        html.AppendLine("<th>السبب</th>")
        html.AppendLine("<th>الحالة</th>")
        html.AppendLine("</tr>")
        html.AppendLine("</thead>")
        html.AppendLine("<tbody>")
        
        For Each leave In leaves
            html.AppendLine("<tr>")
            html.AppendLine($"<td>{leave.LeaveId}</td>")
            html.AppendLine($"<td>{leave.EmployeeName}</td>")
            html.AppendLine($"<td>{leave.GetLeaveTypeText()}</td>")
            html.AppendLine($"<td>{leave.StartDate:dd/MM/yyyy}</td>")
            html.AppendLine($"<td>{leave.EndDate:dd/MM/yyyy}</td>")
            html.AppendLine($"<td>{leave.TotalDays}</td>")
            html.AppendLine($"<td>{leave.Reason}</td>")
            html.AppendLine($"<td>{GetLeaveStatusText(leave.Status)}</td>")
            html.AppendLine("</tr>")
        Next
        
        html.AppendLine("</tbody>")
        html.AppendLine("</table>")
        html.AppendLine("</body>")
        html.AppendLine("</html>")
        
        Return html.ToString()
    End Function
    
    ''' <summary>
    ''' حفظ التقرير كملف HTML
    ''' </summary>
    ''' <param name="htmlContent">محتوى HTML</param>
    ''' <param name="fileName">اسم الملف</param>
    Public Shared Sub SaveHtmlReport(htmlContent As String, fileName As String)
        Try
            Dim filePath As String = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), fileName)
            File.WriteAllText(filePath, htmlContent, Encoding.UTF8)
            
            ' فتح التقرير في المتصفح
            Process.Start(filePath)
        Catch ex As Exception
            MessageBox.Show($"خطأ في حفظ التقرير: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ''' <summary>
    ''' الحصول على النص المقابل لحالة التكليف
    ''' </summary>
    Private Shared Function GetAssignmentStatusText(status As AssignmentStatus) As String
        Select Case status
            Case AssignmentStatus.Draft
                Return "مسودة"
            Case AssignmentStatus.Approved
                Return "معتمد"
            Case AssignmentStatus.Completed
                Return "مكتمل"
            Case AssignmentStatus.Cancelled
                Return "ملغي"
            Case Else
                Return "غير محدد"
        End Select
    End Function
    
    ''' <summary>
    ''' الحصول على النص المقابل لحالة الإجازة
    ''' </summary>
    Private Shared Function GetLeaveStatusText(status As LeaveStatus) As String
        Select Case status
            Case LeaveStatus.Pending
                Return "في الانتظار"
            Case LeaveStatus.Approved
                Return "موافق عليها"
            Case LeaveStatus.Rejected
                Return "مرفوضة"
            Case LeaveStatus.Cancelled
                Return "ملغاة"
            Case LeaveStatus.Completed
                Return "مكتملة"
            Case Else
                Return "غير محدد"
        End Select
    End Function
    
    ''' <summary>
    ''' الحصول على النص المقابل لنوع الإجازة
    ''' </summary>
    Private Shared Function GetLeaveTypeText(leaveType As LeaveType) As String
        Select Case leaveType
            Case LeaveType.Annual
                Return "إجازة اعتيادية"
            Case LeaveType.Sick
                Return "إجازة مرضية"
            Case LeaveType.Emergency
                Return "إجازة اضطرارية"
            Case LeaveType.Maternity
                Return "إجازة أمومة"
            Case LeaveType.Paternity
                Return "إجازة أبوة"
            Case LeaveType.Unpaid
                Return "إجازة بدون راتب"
            Case Else
                Return "غير محدد"
        End Select
    End Function
    
End Class
