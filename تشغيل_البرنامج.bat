@echo off
echo Starting Employee Assignment System...
echo ====================================

REM Check if .NET is installed
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo Error: .NET is not installed
    echo Please install .NET 8.0 or newer
    pause
    exit /b 1
)

REM Build the project
echo Building project...
dotnet build --configuration Release
if errorlevel 1 (
    echo Build failed
    pause
    exit /b 1
)

REM Run the application
echo Starting application...
dotnet run --configuration Release

echo Application started successfully!
pause
