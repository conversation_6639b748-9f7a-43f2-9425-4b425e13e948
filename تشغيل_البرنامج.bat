@echo off
chcp 65001 >nul
echo تشغيل برنامج حسابات الموظفين...
echo ====================================

REM التحقق من وجود .NET
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: .NET غير مثبت على النظام
    echo يرجى تثبيت .NET 8.0 أو أحدث
    pause
    exit /b 1
)

REM بناء المشروع
echo بناء المشروع...
dotnet build --configuration Release --verbosity quiet
if errorlevel 1 (
    echo فشل في بناء المشروع
    pause
    exit /b 1
)

REM تشغيل التطبيق
echo تشغيل التطبيق...
echo البرنامج سيفتح في نافذة منفصلة...
start "" dotnet run --configuration Release

echo تم تشغيل البرنامج بنجاح!
echo يمكنك إغلاق هذه النافذة الآن.
timeout /t 3 >nul
