# تشغيل برنامج حسابات الموظفين
Write-Host "تشغيل برنامج حسابات الموظفين..." -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green

# التحقق من وجود .NET
try {
    $dotnetVersion = dotnet --version
    Write-Host ".NET الإصدار: $dotnetVersion" -ForegroundColor Cyan
} catch {
    Write-Host "خطأ: .NET غير مثبت على النظام" -ForegroundColor Red
    Write-Host "يرجى تثبيت .NET 8.0 أو أحدث" -ForegroundColor Yellow
    Read-Host "اضغط Enter للمتابعة"
    exit 1
}

# بناء المشروع
Write-Host "بناء المشروع..." -ForegroundColor Yellow
try {
    dotnet build --configuration Release --verbosity quiet
    Write-Host "تم بناء المشروع بنجاح!" -ForegroundColor Green
} catch {
    Write-Host "خطأ في بناء المشروع" -ForegroundColor Red
    Read-Host "اضغط Enter للمتابعة"
    exit 1
}

# تشغيل التطبيق
Write-Host "تشغيل التطبيق..." -ForegroundColor Yellow
$exePath = "bin\Release\net8.0-windows\EmployeeAssignmentSystem.exe"

if (Test-Path $exePath) {
    Start-Process $exePath
    Write-Host "تم تشغيل البرنامج بنجاح!" -ForegroundColor Green
    Write-Host "يمكنك إغلاق هذه النافذة الآن." -ForegroundColor Cyan
} else {
    Write-Host "خطأ: لم يتم العثور على الملف التنفيذي" -ForegroundColor Red
    Write-Host "المسار المتوقع: $exePath" -ForegroundColor Yellow
}

Start-Sleep -Seconds 3
