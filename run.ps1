# Employee Assignment System Launcher
Write-Host "========================================" -ForegroundColor Green
Write-Host "    Employee Assignment System" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

Write-Host "Checking system requirements..." -ForegroundColor Yellow

# Check if .NET is installed
try {
    $dotnetVersion = dotnet --version
    Write-Host ".NET Version: $dotnetVersion" -ForegroundColor Cyan
} catch {
    Write-Host "ERROR: .NET is not installed" -ForegroundColor Red
    Write-Host "Please install .NET 8.0 or newer from:" -ForegroundColor Yellow
    Write-Host "https://dotnet.microsoft.com/download" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "Building project..." -ForegroundColor Yellow
try {
    dotnet clean | Out-Null
    dotnet build --configuration Release --verbosity quiet
    Write-Host "Build successful!" -ForegroundColor Green
} catch {
    Write-Host "BUILD FAILED" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "Starting application..." -ForegroundColor Yellow
Write-Host "Note: The program will open in a separate window" -ForegroundColor Cyan

try {
    dotnet run --configuration Release
    Write-Host "Application started successfully!" -ForegroundColor Green
} catch {
    Write-Host "Failed to start application" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Read-Host "Press Enter to exit"
