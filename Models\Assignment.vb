''' <summary>
''' فئة التكليف - تحتوي على بيانات التكليف الرسمي للموظف
''' </summary>
Public Class Assignment
    
    ''' <summary>
    ''' رقم التكليف (مفتاح أساسي)
    ''' </summary>
    Public Property AssignmentId As String
    
    ''' <summary>
    ''' رقم الموظف المكلف
    ''' </summary>
    Public Property EmployeeId As String
    
    ''' <summary>
    ''' اسم الموظف المكلف
    ''' </summary>
    Public Property EmployeeName As String
    
    ''' <summary>
    ''' تاريخ بداية التكليف
    ''' </summary>
    Public Property StartDate As Date
    
    ''' <summary>
    ''' تاريخ نهاية التكليف
    ''' </summary>
    Public Property EndDate As Date
    
    ''' <summary>
    ''' مكان التكليف (المدينة)
    ''' </summary>
    Public Property AssignmentLocation As String
    
    ''' <summary>
    ''' الجهة المكلف إليها
    ''' </summary>
    Public Property AssignmentTo As String
    
    ''' <summary>
    ''' غرض التكليف
    ''' </summary>
    Public Property Purpose As String
    
    ''' <summary>
    ''' قيمة الانتداب اليومي
    ''' </summary>
    Public Property DailyAllowance As Decimal
    
    ''' <summary>
    ''' عدد أيام التكليف (محسوب)
    ''' </summary>
    Public ReadOnly Property TotalDays As Integer
        Get
            Return CalculateTotalDays()
        End Get
    End Property
    
    ''' <summary>
    ''' إجمالي قيمة الانتداب (محسوب)
    ''' </summary>
    Public ReadOnly Property TotalAllowance As Decimal
        Get
            Return TotalDays * DailyAllowance
        End Get
    End Property
    
    ''' <summary>
    ''' حالة التكليف
    ''' </summary>
    Public Property Status As AssignmentStatus
    
    ''' <summary>
    ''' تاريخ إنشاء السجل
    ''' </summary>
    Public Property CreatedDate As Date
    
    ''' <summary>
    ''' ملاحظات إضافية
    ''' </summary>
    Public Property Notes As String
    
    ''' <summary>
    ''' منشئ افتراضي
    ''' </summary>
    Public Sub New()
        Me.CreatedDate = Date.Now
        Me.Status = AssignmentStatus.Draft
    End Sub
    
    ''' <summary>
    ''' منشئ مع المعاملات الأساسية
    ''' </summary>
    Public Sub New(assignmentId As String, employeeId As String, startDate As Date, endDate As Date)
        Me.New()
        Me.AssignmentId = assignmentId
        Me.EmployeeId = employeeId
        Me.StartDate = startDate
        Me.EndDate = endDate
    End Sub
    
    ''' <summary>
    ''' حساب عدد أيام التكليف (باستثناء أيام الجمعة والعطل الرسمية)
    ''' </summary>
    Private Function CalculateTotalDays() As Integer
        If StartDate > EndDate Then
            Return 0
        End If
        
        Dim totalDays As Integer = 0
        Dim currentDate As Date = StartDate
        
        While currentDate <= EndDate
            ' استثناء يوم الجمعة (يوم 5 في الأسبوع)
            If currentDate.DayOfWeek <> DayOfWeek.Friday Then
                ' يمكن إضافة فحص العطل الرسمية هنا
                If Not IsOfficialHoliday(currentDate) Then
                    totalDays += 1
                End If
            End If
            currentDate = currentDate.AddDays(1)
        End While
        
        Return totalDays
    End Function
    
    ''' <summary>
    ''' فحص إذا كان التاريخ عطلة رسمية
    ''' </summary>
    Private Function IsOfficialHoliday(checkDate As Date) As Boolean
        ' قائمة العطل الرسمية في المملكة العربية السعودية
        ' يمكن تخصيصها حسب الحاجة
        Dim holidays As New List(Of Date) From {
            New Date(checkDate.Year, 9, 23), ' اليوم الوطني
            New Date(checkDate.Year, 5, 1)   ' عيد العمال (إذا كان معتمد)
        }
        
        ' يمكن إضافة العطل المتغيرة مثل عيد الفطر وعيد الأضحى
        ' هذا يتطلب حساب التقويم الهجري
        
        Return holidays.Contains(checkDate.Date)
    End Function
    
    ''' <summary>
    ''' التحقق من صحة بيانات التكليف
    ''' </summary>
    Public Function IsValid() As Boolean
        Return Not String.IsNullOrWhiteSpace(AssignmentId) AndAlso
               Not String.IsNullOrWhiteSpace(EmployeeId) AndAlso
               StartDate <= EndDate AndAlso
               DailyAllowance >= 0
    End Function
    
    ''' <summary>
    ''' إرجاع تمثيل نصي للتكليف
    ''' </summary>
    Public Overrides Function ToString() As String
        Return $"{AssignmentId} - {EmployeeName} ({StartDate:dd/MM/yyyy} - {EndDate:dd/MM/yyyy})"
    End Function
    
End Class

''' <summary>
''' حالات التكليف
''' </summary>
Public Enum AssignmentStatus
    ''' <summary>
    ''' مسودة
    ''' </summary>
    Draft = 0
    
    ''' <summary>
    ''' معتمد
    ''' </summary>
    Approved = 1
    
    ''' <summary>
    ''' مكتمل
    ''' </summary>
    Completed = 2
    
    ''' <summary>
    ''' ملغي
    ''' </summary>
    Cancelled = 3
End Enum
