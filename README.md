# نظام حسابات الموظفين - التكليفات والإجازات

## وصف البرنامج

برنامج شامل لحساب التكليفات الرسمية والإجازات للموظفين في المملكة العربية السعودية، مطور باستخدام VB.NET مع Windows Forms.

## المميزات الرئيسية

### 🏢 إدارة الموظفين
- إضافة وتعديل بيانات الموظفين
- تخزين المعلومات الأساسية (الاسم، رقم الهوية، المرتبة، الراتب)
- إدارة أرصدة الإجازات المختلفة

### 📋 حساب التكليفات
- حساب عدد أيام التكليف الفعلية
- استثناء أيام الجمعة والعطل الرسمية
- حساب قيمة الانتداب اليومي حسب المرتبة
- إجمالي قيمة الانتداب للتكليف

### 🏖️ إدارة الإجازات
- أنواع الإجازات المختلفة (اعتيادية، مرضية، اضطرارية، أمومة، أبوة، بدون راتب)
- حساب عدد أيام الإجازة الفعلية
- إدارة أرصدة الإجازات
- التحقق من توفر الرصيد الكافي

### 📊 التقارير
- تقارير التكليفات مع الإحصائيات
- تقارير الإجازات مع التفاصيل
- إنتاج التقارير بصيغة HTML
- فتح التقارير في المتصفح تلقائياً

## متطلبات النظام

- Windows 7 أو أحدث
- .NET Framework 4.7.2 أو أحدث
- Visual Studio 2017 أو أحدث (للتطوير)

## هيكل المشروع

```
EmployeeAssignmentSystem/
├── Models/                 # نماذج البيانات
│   ├── Employee.vb        # نموذج الموظف
│   ├── Assignment.vb      # نموذج التكليف
│   └── Leave.vb           # نموذج الإجازة
├── Business/              # منطق العمل
│   ├── AssignmentCalculator.vb  # حاسبة التكليفات
│   └── LeaveCalculator.vb       # حاسبة الإجازات
├── Forms/                 # واجهات المستخدم
│   ├── MainForm.vb        # النموذج الرئيسي
│   └── MainForm.Designer.vb
├── Reports/               # التقارير
│   └── ReportGenerator.vb # مولد التقارير
└── My Project/           # ملفات المشروع
```

## كيفية الاستخدام

### 1. إدارة الموظفين
1. انتقل إلى تبويب "إدارة الموظفين"
2. أدخل بيانات الموظف الجديد
3. اختر المرتبة الوظيفية (يتم حساب الانتداب اليومي تلقائياً)
4. اضغط "إضافة موظف"

### 2. حساب التكليفات
1. انتقل إلى تبويب "التكليفات"
2. اختر الموظف من القائمة
3. أدخل تاريخ البداية والنهاية
4. أدخل مكان التكليف والجهة المكلف إليها
5. اضغط "حساب التكليف"
6. ستظهر النتائج في قسم "نتائج الحساب"

### 3. حساب الإجازات
1. انتقل إلى تبويب "الإجازات"
2. اختر الموظف ونوع الإجازة
3. أدخل تاريخ البداية والنهاية
4. أدخل سبب الإجازة
5. اضغط "حساب الإجازة"
6. ستظهر النتائج مع الرصيد المتبقي

### 4. إنتاج التقارير
- اضغط "تقرير التكليفات" لإنتاج تقرير شامل للتكليفات
- اضغط "تقرير الإجازات" لإنتاج تقرير شامل للإجازات
- سيتم حفظ التقرير على سطح المكتب وفتحه تلقائياً

## قيم الانتداب اليومي حسب المرتبة

| المرتبة | الانتداب اليومي (ريال) |
|---------|-------------------|
| الأولى   | 500              |
| الثانية  | 450              |
| الثالثة  | 400              |
| الرابعة  | 350              |
| الخامسة  | 300              |
| السادسة  | 250              |
| السابعة  | 200              |
| الثامنة  | 180              |
| التاسعة  | 160              |
| العاشرة  | 150              |

## العطل الرسمية المعتمدة

- اليوم الوطني السعودي (23 سبتمبر)
- يوم التأسيس (22 فبراير)
- عيد العمال (1 مايو) - اختياري

## الأرصدة الافتراضية للإجازات

- الإجازة الاعتيادية: 30 يوم سنوياً
- الإجازة المرضية: 30 يوم سنوياً
- الإجازة الاضطرارية: 5 أيام سنوياً
- إجازة الأمومة: 70 يوم
- إجازة الأبوة: 3 أيام

## التطوير والصيانة

### إضافة عطلة رسمية جديدة
```vb
' في ملف AssignmentCalculator.vb
Private Shared ReadOnly OfficialHolidays As New Dictionary(Of String, String) From {
    {"09-23", "اليوم الوطني السعودي"},
    {"02-22", "يوم التأسيس"},
    {"MM-DD", "اسم العطلة الجديدة"}  ' أضف هنا
}
```

### تعديل قيم الانتداب
```vb
' في ملف AssignmentCalculator.vb
Private Shared ReadOnly DailyAllowanceByGrade As New Dictionary(Of String, Decimal) From {
    {"المرتبة", القيمة_الجديدة}
}
```

## الدعم والمساعدة

للحصول على الدعم أو الإبلاغ عن مشاكل:
- راجع الكود المصدري في المجلدات المختلفة
- تحقق من رسائل الخطأ في النوافذ المنبثقة
- تأكد من صحة البيانات المدخلة

## الترخيص

هذا البرنامج مطور للاستخدام الداخلي في المؤسسات الحكومية السعودية.

---

**تم التطوير بواسطة:** فريق تطوير الأنظمة  
**التاريخ:** 2024  
**الإصدار:** 1.0.0
