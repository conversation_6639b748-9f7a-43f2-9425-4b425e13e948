تعليمات تشغيل برنامج حسابات الموظفين - التكليفات والإجازات
================================================================

المتطلبات:
----------
1. نظام التشغيل: Windows 10 أو أحدث
2. .NET 8.0 أو أحدث
3. Visual Studio 2022 أو أحدث (للتطوير - اختياري)

خطوات التشغيل السريع:
--------------------

الطريقة الأسهل:
1. انقر مرتين على ملف "تشغيل_البرنامج.bat"
2. انتظر حتى يكتمل البناء
3. سيفتح البرنامج تلقائياً

الطريقة البديلة:
1. افتح Command Prompt أو PowerShell
2. انتقل إلى مجلد المشروع
3. اكتب: dotnet run
4. اضغط Enter

خطوات التشغيل التفصيلية:
------------------------

1. التحقق من .NET:
   - افتح Command Prompt
   - اكتب: dotnet --version
   - يجب أن تظهر النسخة 8.0 أو أحدث

2. بناء المشروع:
   - في مجلد المشروع، اكتب: dotnet build
   - تأكد من عدم وجود أخطاء

3. تشغيل البرنامج:
   - اكتب: dotnet run
   - أو شغل الملف التنفيذي من: bin\Debug\net8.0-windows\EmployeeAssignmentSystem.exe

استخدام البرنامج:
-----------------

1. إضافة موظف جديد:
   - انتقل إلى تبويب "إدارة الموظفين"
   - املأ جميع الحقول المطلوبة
   - اضغط "إضافة موظف"

2. حساب تكليف:
   - انتقل إلى تبويب "التكليفات"
   - اختر الموظف من القائمة
   - أدخل تواريخ التكليف والتفاصيل
   - اضغط "حساب التكليف"

3. حساب إجازة:
   - انتقل إلى تبويب "الإجازات"
   - اختر الموظف ونوع الإجازة
   - أدخل تواريخ الإجازة والسبب
   - اضغط "حساب الإجازة"

4. إنتاج التقارير:
   - اضغط "تقرير التكليفات" أو "تقرير الإجازات"
   - سيتم حفظ التقرير على سطح المكتب
   - سيفتح التقرير تلقائياً في المتصفح

5. تشغيل الاختبارات:
   - في تبويب "إدارة الموظفين"
   - اضغط "تشغيل الاختبارات"
   - ستظهر نتائج الاختبارات في نافذة وحدة التحكم

ملاحظات مهمة:
--------------
- تأكد من إدخال رقم هوية صحيح (10 أرقام)
- تأكد من أن تاريخ النهاية بعد تاريخ البداية
- يتم حفظ البيانات في الذاكرة فقط (لا يوجد قاعدة بيانات)
- عند إغلاق البرنامج ستفقد جميع البيانات

استكشاف الأخطاء:
-----------------
- إذا ظهرت رسالة خطأ، اقرأها بعناية
- تأكد من صحة البيانات المدخلة
- تأكد من اختيار موظف قبل الحساب
- في حالة الأخطاء المستمرة، أعد تشغيل البرنامج

الدعم الفني:
------------
للحصول على المساعدة:
1. راجع ملف README.md
2. تحقق من الكود المصدري
3. شغل الاختبارات للتأكد من سلامة النظام

تاريخ الإنشاء: 2024
الإصدار: 1.0.0
