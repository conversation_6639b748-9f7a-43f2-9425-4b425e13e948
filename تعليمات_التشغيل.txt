تعليمات تشغيل برنامج حسابات الموظفين - التكليفات والإجازات
================================================================

المتطلبات:
----------
1. نظام التشغيل: Windows 7 أو أحدث
2. .NET Framework 4.7.2 أو أحدث
3. Visual Studio 2017 أو أحدث (للتطوير)

خطوات التشغيل:
--------------

1. فتح المشروع:
   - افتح Visual Studio
   - اختر File > Open > Project/Solution
   - اختر ملف EmployeeAssignmentSystem.vbproj

2. بناء المشروع:
   - اضغط Ctrl+Shift+B أو اختر Build > Build Solution
   - تأكد من عدم وجود أخطاء في البناء

3. تشغيل البرنامج:
   - اضغط F5 أو اختر Debug > Start Debugging
   - أو اضغط Ctrl+F5 للتشغيل بدون تصحيح الأخطاء

استخدام البرنامج:
-----------------

1. إضافة موظف جديد:
   - انتقل إلى تبويب "إدارة الموظفين"
   - املأ جميع الحقول المطلوبة
   - اضغط "إضافة موظف"

2. حساب تكليف:
   - انتقل إلى تبويب "التكليفات"
   - اختر الموظف من القائمة
   - أدخل تواريخ التكليف والتفاصيل
   - اضغط "حساب التكليف"

3. حساب إجازة:
   - انتقل إلى تبويب "الإجازات"
   - اختر الموظف ونوع الإجازة
   - أدخل تواريخ الإجازة والسبب
   - اضغط "حساب الإجازة"

4. إنتاج التقارير:
   - اضغط "تقرير التكليفات" أو "تقرير الإجازات"
   - سيتم حفظ التقرير على سطح المكتب
   - سيفتح التقرير تلقائياً في المتصفح

5. تشغيل الاختبارات:
   - في تبويب "إدارة الموظفين"
   - اضغط "تشغيل الاختبارات"
   - ستظهر نتائج الاختبارات في نافذة وحدة التحكم

ملاحظات مهمة:
--------------
- تأكد من إدخال رقم هوية صحيح (10 أرقام)
- تأكد من أن تاريخ النهاية بعد تاريخ البداية
- يتم حفظ البيانات في الذاكرة فقط (لا يوجد قاعدة بيانات)
- عند إغلاق البرنامج ستفقد جميع البيانات

استكشاف الأخطاء:
-----------------
- إذا ظهرت رسالة خطأ، اقرأها بعناية
- تأكد من صحة البيانات المدخلة
- تأكد من اختيار موظف قبل الحساب
- في حالة الأخطاء المستمرة، أعد تشغيل البرنامج

الدعم الفني:
------------
للحصول على المساعدة:
1. راجع ملف README.md
2. تحقق من الكود المصدري
3. شغل الاختبارات للتأكد من سلامة النظام

تاريخ الإنشاء: 2024
الإصدار: 1.0.0
