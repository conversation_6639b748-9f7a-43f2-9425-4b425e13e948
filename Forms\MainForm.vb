Imports System.Windows.Forms
Imports System.Drawing

''' <summary>
''' النموذج الرئيسي لبرنامج حسابات الموظفين
''' </summary>
Public Class MainForm
    Inherits Form
    
    ''' <summary>
    ''' قائمة الموظفين المحملة في البرنامج
    ''' </summary>
    Private employees As New List(Of Employee)
    
    ''' <summary>
    ''' قائمة التكليفات
    ''' </summary>
    Private assignments As New List(Of Assignment)
    
    ''' <summary>
    ''' قائمة الإجازات
    ''' </summary>
    Private leaves As New List(Of Leave)
    
    ''' <summary>
    ''' تحميل النموذج
    ''' </summary>
    Private Sub MainForm_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        InitializeForm()
        LoadSampleData()
        RefreshEmployeeList()
    End Sub
    
    ''' <summary>
    ''' تهيئة النموذج والعناصر
    ''' </summary>
    Private Sub InitializeForm()
        ' تعيين خصائص النموذج
        Me.Text = "نظام حسابات الموظفين - التكليفات والإجازات"
        Me.WindowState = FormWindowState.Maximized
        Me.RightToLeft = RightToLeft.Yes
        Me.RightToLeftLayout = True
        
        ' تعيين الخط العربي
        Me.Font = New Font("Tahoma", 10, FontStyle.Regular)
        
        ' تهيئة القوائم المنسدلة
        InitializeComboBoxes()
        
        ' تعيين التواريخ الافتراضية
        dtpAssignmentStart.Value = Date.Today
        dtpAssignmentEnd.Value = Date.Today.AddDays(7)
        dtpLeaveStart.Value = Date.Today
        dtpLeaveEnd.Value = Date.Today.AddDays(3)
    End Sub
    
    ''' <summary>
    ''' تهيئة القوائم المنسدلة
    ''' </summary>
    Private Sub InitializeComboBoxes()
        ' قائمة المراتب الوظيفية
        cmbJobGrade.Items.Clear()
        For Each grade In AssignmentCalculator.GetAvailableGrades()
            cmbJobGrade.Items.Add(grade)
        Next
        
        ' قائمة أنواع الإجازات
        cmbLeaveType.Items.Clear()
        cmbLeaveType.Items.Add("إجازة اعتيادية")
        cmbLeaveType.Items.Add("إجازة مرضية")
        cmbLeaveType.Items.Add("إجازة اضطرارية")
        cmbLeaveType.Items.Add("إجازة أمومة")
        cmbLeaveType.Items.Add("إجازة أبوة")
        cmbLeaveType.Items.Add("إجازة بدون راتب")
        
        ' قائمة حالات التكليف
        cmbAssignmentStatus.Items.Clear()
        cmbAssignmentStatus.Items.Add("مسودة")
        cmbAssignmentStatus.Items.Add("معتمد")
        cmbAssignmentStatus.Items.Add("مكتمل")
        cmbAssignmentStatus.Items.Add("ملغي")
    End Sub
    
    ''' <summary>
    ''' تحميل بيانات تجريبية
    ''' </summary>
    Private Sub LoadSampleData()
        ' إضافة موظفين تجريبيين
        employees.Add(New Employee("001", "أحمد محمد العلي", "1234567890") With {
            .JobTitle = "مدير إدارة",
            .JobGrade = "الثالثة",
            .BasicSalary = 15000,
            .DailyAllowance = AssignmentCalculator.GetDailyAllowanceByGrade("الثالثة"),
            .Department = "الإدارة العامة"
        })
        
        employees.Add(New Employee("002", "فاطمة سعد الأحمد", "0987654321") With {
            .JobTitle = "محاسبة",
            .JobGrade = "الخامسة",
            .BasicSalary = 12000,
            .DailyAllowance = AssignmentCalculator.GetDailyAllowanceByGrade("الخامسة"),
            .Department = "الشؤون المالية"
        })
        
        employees.Add(New Employee("003", "خالد عبدالله الزهراني", "1122334455") With {
            .JobTitle = "مطور برمجيات",
            .JobGrade = "السادسة",
            .BasicSalary = 10000,
            .DailyAllowance = AssignmentCalculator.GetDailyAllowanceByGrade("السادسة"),
            .Department = "تقنية المعلومات"
        })
    End Sub
    
    ''' <summary>
    ''' تحديث قائمة الموظفين
    ''' </summary>
    Private Sub RefreshEmployeeList()
        cmbEmployee.Items.Clear()
        For Each emp In employees
            cmbEmployee.Items.Add(emp)
        Next
        
        cmbLeaveEmployee.Items.Clear()
        For Each emp In employees
            cmbLeaveEmployee.Items.Add(emp)
        Next
        
        ' تحديث شبكة البيانات
        RefreshDataGrids()
    End Sub
    
    ''' <summary>
    ''' تحديث شبكات البيانات
    ''' </summary>
    Private Sub RefreshDataGrids()
        ' تحديث شبكة التكليفات
        dgvAssignments.DataSource = Nothing
        dgvAssignments.DataSource = assignments
        
        ' تحديث شبكة الإجازات
        dgvLeaves.DataSource = Nothing
        dgvLeaves.DataSource = leaves
    End Sub
    
    ''' <summary>
    ''' إضافة موظف جديد
    ''' </summary>
    Private Sub btnAddEmployee_Click(sender As Object, e As EventArgs) Handles btnAddEmployee.Click
        Try
            If ValidateEmployeeData() Then
                Dim newEmployee As New Employee(txtEmployeeId.Text, txtEmployeeName.Text, txtNationalId.Text) With {
                    .JobTitle = txtJobTitle.Text,
                    .JobGrade = cmbJobGrade.Text,
                    .BasicSalary = CDec(txtBasicSalary.Text),
                    .DailyAllowance = AssignmentCalculator.GetDailyAllowanceByGrade(cmbJobGrade.Text),
                    .Department = txtDepartment.Text
                }
                
                employees.Add(newEmployee)
                RefreshEmployeeList()
                ClearEmployeeForm()
                
                MessageBox.Show("تم إضافة الموظف بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If
        Catch ex As Exception
            MessageBox.Show($"خطأ في إضافة الموظف: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ''' <summary>
    ''' التحقق من صحة بيانات الموظف
    ''' </summary>
    Private Function ValidateEmployeeData() As Boolean
        If String.IsNullOrWhiteSpace(txtEmployeeId.Text) Then
            MessageBox.Show("رقم الموظف مطلوب", "تحقق", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtEmployeeId.Focus()
            Return False
        End If
        
        If String.IsNullOrWhiteSpace(txtEmployeeName.Text) Then
            MessageBox.Show("اسم الموظف مطلوب", "تحقق", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtEmployeeName.Focus()
            Return False
        End If
        
        If String.IsNullOrWhiteSpace(txtNationalId.Text) OrElse txtNationalId.Text.Length <> 10 Then
            MessageBox.Show("رقم الهوية يجب أن يكون 10 أرقام", "تحقق", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtNationalId.Focus()
            Return False
        End If
        
        If employees.Any(Function(e) e.EmployeeId = txtEmployeeId.Text) Then
            MessageBox.Show("رقم الموظف موجود مسبقاً", "تحقق", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtEmployeeId.Focus()
            Return False
        End If
        
        Return True
    End Function
    
    ''' <summary>
    ''' مسح نموذج الموظف
    ''' </summary>
    Private Sub ClearEmployeeForm()
        txtEmployeeId.Clear()
        txtEmployeeName.Clear()
        txtNationalId.Clear()
        txtJobTitle.Clear()
        cmbJobGrade.SelectedIndex = -1
        txtBasicSalary.Clear()
        txtDepartment.Clear()
    End Sub
    
    ''' <summary>
    ''' حساب التكليف
    ''' </summary>
    Private Sub btnCalculateAssignment_Click(sender As Object, e As EventArgs) Handles btnCalculateAssignment.Click
        Try
            If cmbEmployee.SelectedItem Is Nothing Then
                MessageBox.Show("يرجى اختيار موظف", "تحقق", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return
            End If

            Dim selectedEmployee As Employee = CType(cmbEmployee.SelectedItem, Employee)

            Dim assignment As New Assignment() With {
                .AssignmentId = $"T{Date.Now:yyyyMMddHHmmss}",
                .EmployeeId = selectedEmployee.EmployeeId,
                .EmployeeName = selectedEmployee.FullName,
                .StartDate = dtpAssignmentStart.Value,
                .EndDate = dtpAssignmentEnd.Value,
                .AssignmentLocation = txtAssignmentLocation.Text,
                .AssignmentTo = txtAssignmentTo.Text,
                .Purpose = txtPurpose.Text,
                .DailyAllowance = selectedEmployee.DailyAllowance,
                .Status = CType(cmbAssignmentStatus.SelectedIndex, AssignmentStatus)
            }

            Dim result As AssignmentCalculationResult = AssignmentCalculator.CalculateAssignmentDetails(assignment)

            ' عرض النتائج
            lblAssignmentDays.Text = $"عدد أيام التكليف: {result.WorkingDays} يوم"
            lblTotalAllowance.Text = $"إجمالي الانتداب: {result.TotalAllowance:N2} ريال"
            lblExcludedDays.Text = $"الأيام المستثناة: {result.ExcludedFridays + result.ExcludedHolidays} يوم"

            assignments.Add(assignment)
            RefreshDataGrids()

        Catch ex As Exception
            MessageBox.Show($"خطأ في حساب التكليف: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' حساب الإجازة
    ''' </summary>
    Private Sub btnCalculateLeave_Click(sender As Object, e As EventArgs) Handles btnCalculateLeave.Click
        Try
            If cmbLeaveEmployee.SelectedItem Is Nothing Then
                MessageBox.Show("يرجى اختيار موظف", "تحقق", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return
            End If

            Dim selectedEmployee As Employee = CType(cmbLeaveEmployee.SelectedItem, Employee)
            Dim leaveType As LeaveType = CType(cmbLeaveType.SelectedIndex, LeaveType)

            Dim leave As New Leave() With {
                .LeaveId = $"L{Date.Now:yyyyMMddHHmmss}",
                .EmployeeId = selectedEmployee.EmployeeId,
                .EmployeeName = selectedEmployee.FullName,
                .LeaveType = leaveType,
                .StartDate = dtpLeaveStart.Value,
                .EndDate = dtpLeaveEnd.Value,
                .Reason = txtLeaveReason.Text,
                .Status = LeaveStatus.Pending
            }

            Dim result As LeaveCalculationResult = LeaveCalculator.CalculateLeaveDetails(leave, selectedEmployee)

            ' عرض النتائج
            lblLeaveDays.Text = $"عدد أيام الإجازة: {result.WorkingDays} يوم"
            lblCurrentBalance.Text = $"الرصيد الحالي: {result.CurrentBalance} يوم"
            lblBalanceAfter.Text = $"الرصيد بعد الإجازة: {result.BalanceAfterLeave} يوم"

            If Not result.HasSufficientBalance Then
                MessageBox.Show("تحذير: الرصيد غير كافي لهذه الإجازة", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            End If

            leaves.Add(leave)
            RefreshDataGrids()

        Catch ex As Exception
            MessageBox.Show($"خطأ في حساب الإجازة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' إنتاج تقرير التكليفات
    ''' </summary>
    Private Sub btnAssignmentReport_Click(sender As Object, e As EventArgs) Handles btnAssignmentReport.Click
        Try
            If assignments.Count = 0 Then
                MessageBox.Show("لا توجد تكليفات لإنتاج التقرير", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return
            End If

            Dim htmlReport As String = ReportGenerator.GenerateAssignmentReport(assignments, employees)
            Dim fileName As String = $"تقرير_التكليفات_{Date.Now:yyyyMMdd_HHmmss}.html"

            ReportGenerator.SaveHtmlReport(htmlReport, fileName)

            MessageBox.Show("تم إنتاج تقرير التكليفات بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)

        Catch ex As Exception
            MessageBox.Show($"خطأ في إنتاج التقرير: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' إنتاج تقرير الإجازات
    ''' </summary>
    Private Sub btnLeaveReport_Click(sender As Object, e As EventArgs) Handles btnLeaveReport.Click
        Try
            If leaves.Count = 0 Then
                MessageBox.Show("لا توجد إجازات لإنتاج التقرير", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return
            End If

            Dim htmlReport As String = ReportGenerator.GenerateLeaveReport(leaves, employees)
            Dim fileName As String = $"تقرير_الإجازات_{Date.Now:yyyyMMdd_HHmmss}.html"

            ReportGenerator.SaveHtmlReport(htmlReport, fileName)

            MessageBox.Show("تم إنتاج تقرير الإجازات بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)

        Catch ex As Exception
            MessageBox.Show($"خطأ في إنتاج التقرير: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' عرض تفاصيل الموظف المحدد
    ''' </summary>
    Private Sub cmbEmployee_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbEmployee.SelectedIndexChanged
        If cmbEmployee.SelectedItem IsNot Nothing Then
            Dim selectedEmployee As Employee = CType(cmbEmployee.SelectedItem, Employee)

            ' تحديث قيمة الانتداب اليومي
            lblDailyAllowance.Text = $"الانتداب اليومي: {selectedEmployee.DailyAllowance:N2} ريال"
        End If
    End Sub

    ''' <summary>
    ''' عرض تفاصيل الموظف المحدد للإجازة
    ''' </summary>
    Private Sub cmbLeaveEmployee_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbLeaveEmployee.SelectedIndexChanged
        If cmbLeaveEmployee.SelectedItem IsNot Nothing Then
            Dim selectedEmployee As Employee = CType(cmbLeaveEmployee.SelectedItem, Employee)

            ' عرض أرصدة الإجازات
            lblAnnualBalance.Text = $"رصيد الإجازة الاعتيادية: {selectedEmployee.AnnualLeaveBalance} يوم"
            lblSickBalance.Text = $"رصيد الإجازة المرضية: {selectedEmployee.SickLeaveBalance} يوم"
            lblEmergencyBalance.Text = $"رصيد الإجازة الاضطرارية: {selectedEmployee.EmergencyLeaveBalance} يوم"
        End If
    End Sub

    ''' <summary>
    ''' تشغيل اختبارات النظام
    ''' </summary>
    Private Sub btnRunTests_Click(sender As Object, e As EventArgs) Handles btnRunTests.Click
        Try
            ' تشغيل الاختبارات في نافذة منفصلة
            Dim result As DialogResult = MessageBox.Show("هل تريد تشغيل اختبارات النظام؟" & vbCrLf & "ستظهر النتائج في نافذة وحدة التحكم.",
                                                       "تشغيل الاختبارات", MessageBoxButtons.YesNo, MessageBoxIcon.Question)

            If result = DialogResult.Yes Then
                ' إنشاء نافذة وحدة التحكم
                AllocConsole()
                CalculatorTests.RunAllTests()
            End If

        Catch ex As Exception
            MessageBox.Show($"خطأ في تشغيل الاختبارات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' استيراد دالة إنشاء وحدة التحكم من Windows API
    ''' </summary>
    <System.Runtime.InteropServices.DllImport("kernel32.dll")>
    Private Shared Function AllocConsole() As Boolean
    End Function

End Class
