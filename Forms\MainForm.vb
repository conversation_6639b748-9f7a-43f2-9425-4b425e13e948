Imports System
Imports System.Collections.Generic
Imports System.ComponentModel
Imports System.Drawing
Imports System.Linq
Imports System.Windows.Forms

''' <summary>
''' النموذج الرئيسي لبرنامج حسابات الموظفين - التكليفات والإجازات
''' </summary>
Public Class MainForm
    Inherits Form

    #Region "المتغيرات الخاصة"

    ''' <summary>
    ''' قائمة الموظفين المحملة في البرنامج
    ''' </summary>
    Private ReadOnly employees As New List(Of Employee)

    ''' <summary>
    ''' قائمة التكليفات
    ''' </summary>
    Private ReadOnly assignments As New List(Of Assignment)

    ''' <summary>
    ''' قائمة الإجازات
    ''' </summary>
    Private ReadOnly leaves As New List(Of Leave)

    ''' <summary>
    ''' متغير لتتبع حالة التحميل
    ''' </summary>
    Private isLoading As Boolean = True

    #End Region
    
    #Region "أحداث النموذج"

    ''' <summary>
    ''' تحميل النموذج وتهيئة جميع المكونات
    ''' </summary>
    Private Sub MainForm_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            isLoading = True

            ' تهيئة النموذج
            InitializeForm()

            ' تحميل البيانات التجريبية
            LoadSampleData()

            ' تحديث القوائم
            RefreshAllLists()

            ' تعيين القيم الافتراضية
            SetDefaultValues()

            isLoading = False

            ' عرض رسالة ترحيب
            ShowWelcomeMessage()

        Catch ex As Exception
            HandleError("خطأ في تحميل النموذج", ex)
        End Try
    End Sub

    ''' <summary>
    ''' معالجة إغلاق النموذج
    ''' </summary>
    Private Sub MainForm_FormClosing(sender As Object, e As FormClosingEventArgs) Handles MyBase.FormClosing
        Try
            ' يمكن إضافة حفظ البيانات هنا إذا لزم الأمر
            Dim result = MessageBox.Show("هل تريد إغلاق البرنامج؟", "تأكيد الإغلاق",
                                       MessageBoxButtons.YesNo, MessageBoxIcon.Question)

            If result = DialogResult.No Then
                e.Cancel = True
            End If

        Catch ex As Exception
            HandleError("خطأ في إغلاق النموذج", ex)
        End Try
    End Sub

    #End Region
    
    #Region "دوال التهيئة"

    ''' <summary>
    ''' تهيئة النموذج والعناصر
    ''' </summary>
    Private Sub InitializeForm()
        Try
            ' تعيين خصائص النموذج
            Me.Text = "نظام حسابات الموظفين - التكليفات والإجازات"
            Me.WindowState = FormWindowState.Maximized
            Me.RightToLeft = RightToLeft.Yes
            Me.RightToLeftLayout = True
            Me.Icon = GetApplicationIcon()

            ' تعيين الخط العربي
            Me.Font = New Font("Tahoma", 10, FontStyle.Regular)

            ' تهيئة القوائم المنسدلة
            InitializeComboBoxes()

            ' تهيئة شبكات البيانات
            InitializeDataGrids()

            ' تعيين ألوان العناصر
            ApplyCustomStyles()

        Catch ex As Exception
            HandleError("خطأ في تهيئة النموذج", ex)
        End Try
    End Sub

    ''' <summary>
    ''' تعيين القيم الافتراضية للعناصر
    ''' </summary>
    Private Sub SetDefaultValues()
        Try
            ' تعيين التواريخ الافتراضية
            dtpAssignmentStart.Value = Date.Today
            dtpAssignmentEnd.Value = Date.Today.AddDays(7)
            dtpLeaveStart.Value = Date.Today
            dtpLeaveEnd.Value = Date.Today.AddDays(3)

            ' تعيين القيم الافتراضية للقوائم المنسدلة
            If cmbJobGrade.Items.Count > 0 Then cmbJobGrade.SelectedIndex = 0
            If cmbAssignmentStatus.Items.Count > 0 Then cmbAssignmentStatus.SelectedIndex = 0
            If cmbLeaveType.Items.Count > 0 Then cmbLeaveType.SelectedIndex = 0

            ' تعيين النصوص الافتراضية
            lblAssignmentDays.Text = "عدد أيام التكليف: --"
            lblTotalAllowance.Text = "إجمالي الانتداب: --"
            lblExcludedDays.Text = "الأيام المستثناة: --"
            lblDailyAllowance.Text = "الانتداب اليومي: --"

            lblLeaveDays.Text = "عدد أيام الإجازة: --"
            lblCurrentBalance.Text = "الرصيد الحالي: --"
            lblBalanceAfter.Text = "الرصيد بعد الإجازة: --"
            lblAnnualBalance.Text = "رصيد الإجازة الاعتيادية: --"
            lblSickBalance.Text = "رصيد الإجازة المرضية: --"
            lblEmergencyBalance.Text = "رصيد الإجازة الاضطرارية: --"

        Catch ex As Exception
            HandleError("خطأ في تعيين القيم الافتراضية", ex)
        End Try
    End Sub

    ''' <summary>
    ''' الحصول على أيقونة التطبيق
    ''' </summary>
    Private Function GetApplicationIcon() As Icon
        Try
            Dim iconPath As String = IO.Path.Combine(Application.StartupPath, "app.ico")
            If IO.File.Exists(iconPath) Then
                Return New Icon(iconPath)
            End If
        Catch
            ' تجاهل الخطأ واستخدام الأيقونة الافتراضية
        End Try

        Return SystemIcons.Application
    End Function

    ''' <summary>
    ''' تطبيق التنسيقات المخصصة على العناصر
    ''' </summary>
    Private Sub ApplyCustomStyles()
        Try
            ' ألوان الأزرار
            btnAddEmployee.BackColor = Color.FromArgb(0, 123, 255)
            btnAddEmployee.ForeColor = Color.White
            btnAddEmployee.FlatStyle = FlatStyle.Flat

            btnCalculateAssignment.BackColor = Color.FromArgb(40, 167, 69)
            btnCalculateAssignment.ForeColor = Color.White
            btnCalculateAssignment.FlatStyle = FlatStyle.Flat

            btnCalculateLeave.BackColor = Color.FromArgb(220, 53, 69)
            btnCalculateLeave.ForeColor = Color.White
            btnCalculateLeave.FlatStyle = FlatStyle.Flat

            btnAssignmentReport.BackColor = Color.FromArgb(108, 117, 125)
            btnAssignmentReport.ForeColor = Color.White
            btnAssignmentReport.FlatStyle = FlatStyle.Flat

            btnLeaveReport.BackColor = Color.FromArgb(108, 117, 125)
            btnLeaveReport.ForeColor = Color.White
            btnLeaveReport.FlatStyle = FlatStyle.Flat

            btnRunTests.BackColor = Color.FromArgb(255, 193, 7)
            btnRunTests.ForeColor = Color.Black
            btnRunTests.FlatStyle = FlatStyle.Flat

            ' ألوان التسميات
            lblAssignmentDays.ForeColor = Color.FromArgb(0, 123, 255)
            lblTotalAllowance.ForeColor = Color.FromArgb(40, 167, 69)
            lblExcludedDays.ForeColor = Color.FromArgb(108, 117, 125)

            lblLeaveDays.ForeColor = Color.FromArgb(0, 123, 255)
            lblCurrentBalance.ForeColor = Color.FromArgb(40, 167, 69)
            lblBalanceAfter.ForeColor = Color.FromArgb(220, 53, 69)

        Catch ex As Exception
            ' تجاهل أخطاء التنسيق
        End Try
    End Sub

    #End Region
    
    ''' <summary>
    ''' تهيئة القوائم المنسدلة
    ''' </summary>
    Private Sub InitializeComboBoxes()
        Try
            ' قائمة المراتب الوظيفية
            cmbJobGrade.Items.Clear()
            For Each grade In AssignmentCalculator.GetAvailableGrades()
                cmbJobGrade.Items.Add(grade)
            Next

            ' قائمة أنواع الإجازات
            cmbLeaveType.Items.Clear()
            cmbLeaveType.Items.AddRange({
                "إجازة اعتيادية",
                "إجازة مرضية",
                "إجازة اضطرارية",
                "إجازة أمومة",
                "إجازة أبوة",
                "إجازة بدون راتب"
            })

            ' قائمة حالات التكليف
            cmbAssignmentStatus.Items.Clear()
            cmbAssignmentStatus.Items.AddRange({
                "مسودة",
                "معتمد",
                "مكتمل",
                "ملغي"
            })

            ' تعيين خصائص القوائم المنسدلة
            cmbJobGrade.DropDownStyle = ComboBoxStyle.DropDownList
            cmbLeaveType.DropDownStyle = ComboBoxStyle.DropDownList
            cmbAssignmentStatus.DropDownStyle = ComboBoxStyle.DropDownList
            cmbEmployee.DropDownStyle = ComboBoxStyle.DropDownList
            cmbLeaveEmployee.DropDownStyle = ComboBoxStyle.DropDownList

        Catch ex As Exception
            HandleError("خطأ في تهيئة القوائم المنسدلة", ex)
        End Try
    End Sub

    ''' <summary>
    ''' تهيئة شبكات البيانات
    ''' </summary>
    Private Sub InitializeDataGrids()
        Try
            ' إعدادات شبكة التكليفات
            With dgvAssignments
                .AllowUserToAddRows = False
                .AllowUserToDeleteRows = False
                .ReadOnly = True
                .AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
                .SelectionMode = DataGridViewSelectionMode.FullRowSelect
                .MultiSelect = False
                .RowHeadersVisible = False
                .BackgroundColor = Color.White
                .GridColor = Color.LightGray
                .DefaultCellStyle.Font = New Font("Tahoma", 9)
                .ColumnHeadersDefaultCellStyle.Font = New Font("Tahoma", 9, FontStyle.Bold)
                .ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(240, 240, 240)
            End With

            ' إعدادات شبكة الإجازات
            With dgvLeaves
                .AllowUserToAddRows = False
                .AllowUserToDeleteRows = False
                .ReadOnly = True
                .AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
                .SelectionMode = DataGridViewSelectionMode.FullRowSelect
                .MultiSelect = False
                .RowHeadersVisible = False
                .BackgroundColor = Color.White
                .GridColor = Color.LightGray
                .DefaultCellStyle.Font = New Font("Tahoma", 9)
                .ColumnHeadersDefaultCellStyle.Font = New Font("Tahoma", 9, FontStyle.Bold)
                .ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(240, 240, 240)
            End With

        Catch ex As Exception
            HandleError("خطأ في تهيئة شبكات البيانات", ex)
        End Try
    End Sub
    
    ''' <summary>
    ''' تحميل بيانات تجريبية
    ''' </summary>
    Private Sub LoadSampleData()
        Try
            ' مسح البيانات الحالية
            employees.Clear()
            assignments.Clear()
            leaves.Clear()

            ' إضافة موظفين تجريبيين
            employees.AddRange({
                New Employee("001", "أحمد محمد العلي", "1234567890") With {
                    .JobTitle = "مدير إدارة",
                    .JobGrade = "الثالثة",
                    .BasicSalary = 15000,
                    .DailyAllowance = AssignmentCalculator.GetDailyAllowanceByGrade("الثالثة"),
                    .Department = "الإدارة العامة",
                    .HireDate = New Date(2020, 1, 15)
                },
                New Employee("002", "فاطمة سعد الأحمد", "0987654321") With {
                    .JobTitle = "محاسبة",
                    .JobGrade = "الخامسة",
                    .BasicSalary = 12000,
                    .DailyAllowance = AssignmentCalculator.GetDailyAllowanceByGrade("الخامسة"),
                    .Department = "الشؤون المالية",
                    .HireDate = New Date(2019, 6, 10)
                },
                New Employee("003", "خالد عبدالله الزهراني", "1122334455") With {
                    .JobTitle = "مطور برمجيات",
                    .JobGrade = "السادسة",
                    .BasicSalary = 10000,
                    .DailyAllowance = AssignmentCalculator.GetDailyAllowanceByGrade("السادسة"),
                    .Department = "تقنية المعلومات",
                    .HireDate = New Date(2021, 3, 20)
                },
                New Employee("004", "نورا علي الحربي", "5566778899") With {
                    .JobTitle = "أخصائية موارد بشرية",
                    .JobGrade = "السابعة",
                    .BasicSalary = 8500,
                    .DailyAllowance = AssignmentCalculator.GetDailyAllowanceByGrade("السابعة"),
                    .Department = "الموارد البشرية",
                    .HireDate = New Date(2022, 8, 5)
                },
                New Employee("005", "محمد سالم القحطاني", "9988776655") With {
                    .JobTitle = "مساعد إداري",
                    .JobGrade = "التاسعة",
                    .BasicSalary = 6000,
                    .DailyAllowance = AssignmentCalculator.GetDailyAllowanceByGrade("التاسعة"),
                    .Department = "الخدمات الإدارية",
                    .HireDate = New Date(2023, 2, 12)
                }
            })

            ' إضافة بعض التكليفات التجريبية
            assignments.AddRange({
                New Assignment("T001", "001", Date.Today.AddDays(-30), Date.Today.AddDays(-25)) With {
                    .EmployeeName = "أحمد محمد العلي",
                    .AssignmentLocation = "الرياض",
                    .AssignmentTo = "وزارة التعليم",
                    .Purpose = "اجتماع تنسيقي",
                    .DailyAllowance = 400,
                    .Status = AssignmentStatus.Completed
                },
                New Assignment("T002", "002", Date.Today.AddDays(-15), Date.Today.AddDays(-10)) With {
                    .EmployeeName = "فاطمة سعد الأحمد",
                    .AssignmentLocation = "جدة",
                    .AssignmentTo = "وزارة المالية",
                    .Purpose = "مراجعة حسابات",
                    .DailyAllowance = 300,
                    .Status = AssignmentStatus.Completed
                }
            })

            ' إضافة بعض الإجازات التجريبية
            leaves.AddRange({
                New Leave("L001", "001", LeaveType.Annual, Date.Today.AddDays(-60), Date.Today.AddDays(-56)) With {
                    .EmployeeName = "أحمد محمد العلي",
                    .Reason = "إجازة سنوية",
                    .Status = LeaveStatus.Completed
                },
                New Leave("L002", "003", LeaveType.Sick, Date.Today.AddDays(-20), Date.Today.AddDays(-18)) With {
                    .EmployeeName = "خالد عبدالله الزهراني",
                    .Reason = "إجازة مرضية",
                    .Status = LeaveStatus.Completed
                }
            })

        Catch ex As Exception
            HandleError("خطأ في تحميل البيانات التجريبية", ex)
        End Try
    End Sub
    
    ''' <summary>
    ''' تحديث جميع القوائم والشبكات
    ''' </summary>
    Private Sub RefreshAllLists()
        Try
            RefreshEmployeeLists()
            RefreshDataGrids()
        Catch ex As Exception
            HandleError("خطأ في تحديث القوائم", ex)
        End Try
    End Sub

    ''' <summary>
    ''' تحديث قوائم الموظفين
    ''' </summary>
    Private Sub RefreshEmployeeLists()
        Try
            ' حفظ الاختيار الحالي
            Dim selectedEmployee As Employee = TryCast(cmbEmployee.SelectedItem, Employee)
            Dim selectedLeaveEmployee As Employee = TryCast(cmbLeaveEmployee.SelectedItem, Employee)

            ' تحديث قائمة موظفي التكليفات
            cmbEmployee.Items.Clear()
            For Each emp In employees.Where(Function(e) e.IsActive).OrderBy(Function(e) e.FullName)
                cmbEmployee.Items.Add(emp)
            Next

            ' تحديث قائمة موظفي الإجازات
            cmbLeaveEmployee.Items.Clear()
            For Each emp In employees.Where(Function(e) e.IsActive).OrderBy(Function(e) e.FullName)
                cmbLeaveEmployee.Items.Add(emp)
            Next

            ' استعادة الاختيار إن أمكن
            If selectedEmployee IsNot Nothing Then
                Dim index = cmbEmployee.Items.Cast(Of Employee)().ToList().FindIndex(Function(e) e.EmployeeId = selectedEmployee.EmployeeId)
                If index >= 0 Then cmbEmployee.SelectedIndex = index
            End If

            If selectedLeaveEmployee IsNot Nothing Then
                Dim index = cmbLeaveEmployee.Items.Cast(Of Employee)().ToList().FindIndex(Function(e) e.EmployeeId = selectedLeaveEmployee.EmployeeId)
                If index >= 0 Then cmbLeaveEmployee.SelectedIndex = index
            End If

        Catch ex As Exception
            HandleError("خطأ في تحديث قوائم الموظفين", ex)
        End Try
    End Sub
    
    ''' <summary>
    ''' تحديث شبكات البيانات
    ''' </summary>
    Private Sub RefreshDataGrids()
        Try
            ' تحديث شبكة التكليفات
            RefreshAssignmentsGrid()

            ' تحديث شبكة الإجازات
            RefreshLeavesGrid()

        Catch ex As Exception
            HandleError("خطأ في تحديث شبكات البيانات", ex)
        End Try
    End Sub

    ''' <summary>
    ''' تحديث شبكة التكليفات
    ''' </summary>
    Private Sub RefreshAssignmentsGrid()
        Try
            ' إنشاء قائمة مخصصة للعرض
            Dim displayList = assignments.Select(Function(a) New With {
                .رقم_التكليف = a.AssignmentId,
                .اسم_الموظف = a.EmployeeName,
                .تاريخ_البداية = a.StartDate.ToString("dd/MM/yyyy"),
                .تاريخ_النهاية = a.EndDate.ToString("dd/MM/yyyy"),
                .عدد_الأيام = a.TotalDays,
                .مكان_التكليف = a.AssignmentLocation,
                .الانتداب_اليومي = $"{a.DailyAllowance:N0} ريال",
                .إجمالي_الانتداب = $"{a.TotalAllowance:N0} ريال",
                .الحالة = GetAssignmentStatusText(a.Status)
            }).ToList()

            dgvAssignments.DataSource = displayList

            ' تنسيق الأعمدة
            If dgvAssignments.Columns.Count > 0 Then
                dgvAssignments.Columns("رقم_التكليف").Width = 100
                dgvAssignments.Columns("اسم_الموظف").Width = 150
                dgvAssignments.Columns("تاريخ_البداية").Width = 100
                dgvAssignments.Columns("تاريخ_النهاية").Width = 100
                dgvAssignments.Columns("عدد_الأيام").Width = 80
                dgvAssignments.Columns("مكان_التكليف").Width = 120
                dgvAssignments.Columns("الانتداب_اليومي").Width = 100
                dgvAssignments.Columns("إجمالي_الانتداب").Width = 120
                dgvAssignments.Columns("الحالة").Width = 80
            End If

        Catch ex As Exception
            HandleError("خطأ في تحديث شبكة التكليفات", ex)
        End Try
    End Sub

    ''' <summary>
    ''' تحديث شبكة الإجازات
    ''' </summary>
    Private Sub RefreshLeavesGrid()
        Try
            ' إنشاء قائمة مخصصة للعرض
            Dim displayList = leaves.Select(Function(l) New With {
                .رقم_الإجازة = l.LeaveId,
                .اسم_الموظف = l.EmployeeName,
                .نوع_الإجازة = l.GetLeaveTypeText(),
                .تاريخ_البداية = l.StartDate.ToString("dd/MM/yyyy"),
                .تاريخ_النهاية = l.EndDate.ToString("dd/MM/yyyy"),
                .عدد_الأيام = l.TotalDays,
                .السبب = If(String.IsNullOrEmpty(l.Reason), "غير محدد", l.Reason),
                .الحالة = GetLeaveStatusText(l.Status),
                .مدفوعة_الأجر = If(l.IsPaid, "نعم", "لا")
            }).ToList()

            dgvLeaves.DataSource = displayList

            ' تنسيق الأعمدة
            If dgvLeaves.Columns.Count > 0 Then
                dgvLeaves.Columns("رقم_الإجازة").Width = 100
                dgvLeaves.Columns("اسم_الموظف").Width = 150
                dgvLeaves.Columns("نوع_الإجازة").Width = 120
                dgvLeaves.Columns("تاريخ_البداية").Width = 100
                dgvLeaves.Columns("تاريخ_النهاية").Width = 100
                dgvLeaves.Columns("عدد_الأيام").Width = 80
                dgvLeaves.Columns("السبب").Width = 200
                dgvLeaves.Columns("الحالة").Width = 80
                dgvLeaves.Columns("مدفوعة_الأجر").Width = 80
            End If

        Catch ex As Exception
            HandleError("خطأ في تحديث شبكة الإجازات", ex)
        End Try
    End Sub

    #Region "دوال مساعدة"

    ''' <summary>
    ''' معالجة الأخطاء وعرض رسائل مناسبة
    ''' </summary>
    ''' <param name="context">سياق الخطأ</param>
    ''' <param name="ex">الاستثناء</param>
    Private Sub HandleError(context As String, ex As Exception)
        Try
            Dim errorMessage As String = $"{context}:{vbCrLf}{vbCrLf}{ex.Message}"

            If ex.InnerException IsNot Nothing Then
                errorMessage &= $"{vbCrLf}{vbCrLf}تفاصيل إضافية: {ex.InnerException.Message}"
            End If

            MessageBox.Show(errorMessage, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)

            ' تسجيل الخطأ
            LogError(context, ex)

        Catch
            ' تجاهل أخطاء معالجة الأخطاء
        End Try
    End Sub

    ''' <summary>
    ''' تسجيل الأخطاء في ملف
    ''' </summary>
    ''' <param name="context">سياق الخطأ</param>
    ''' <param name="ex">الاستثناء</param>
    Private Sub LogError(context As String, ex As Exception)
        Try
            Dim logPath As String = IO.Path.Combine(Application.StartupPath, "error_log.txt")
            Dim logEntry As String = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {context}: {ex.Message}{vbCrLf}{ex.StackTrace}{vbCrLf}{New String("="c, 50)}{vbCrLf}"
            IO.File.AppendAllText(logPath, logEntry)
        Catch
            ' تجاهل أخطاء التسجيل
        End Try
    End Sub

    ''' <summary>
    ''' عرض رسالة ترحيب
    ''' </summary>
    Private Sub ShowWelcomeMessage()
        Try
            Dim welcomeMessage As String = "مرحباً بك في نظام حسابات الموظفين" & vbCrLf & vbCrLf &
                                         "المميزات المتاحة:" & vbCrLf &
                                         "• إدارة بيانات الموظفين" & vbCrLf &
                                         "• حساب التكليفات والانتداب" & vbCrLf &
                                         "• إدارة الإجازات والأرصدة" & vbCrLf &
                                         "• إنتاج التقارير" & vbCrLf &
                                         "• اختبار النظام" & vbCrLf & vbCrLf &
                                         $"تم تحميل {employees.Count} موظف، {assignments.Count} تكليف، {leaves.Count} إجازة"

            MessageBox.Show(welcomeMessage, "مرحباً", MessageBoxButtons.OK, MessageBoxIcon.Information)

        Catch ex As Exception
            ' تجاهل أخطاء رسالة الترحيب
        End Try
    End Sub

    ''' <summary>
    ''' الحصول على نص حالة التكليف
    ''' </summary>
    Private Function GetAssignmentStatusText(status As AssignmentStatus) As String
        Select Case status
            Case AssignmentStatus.Draft
                Return "مسودة"
            Case AssignmentStatus.Approved
                Return "معتمد"
            Case AssignmentStatus.Completed
                Return "مكتمل"
            Case AssignmentStatus.Cancelled
                Return "ملغي"
            Case Else
                Return "غير محدد"
        End Select
    End Function

    ''' <summary>
    ''' الحصول على نص حالة الإجازة
    ''' </summary>
    Private Function GetLeaveStatusText(status As LeaveStatus) As String
        Select Case status
            Case LeaveStatus.Pending
                Return "في الانتظار"
            Case LeaveStatus.Approved
                Return "موافق عليها"
            Case LeaveStatus.Rejected
                Return "مرفوضة"
            Case LeaveStatus.Cancelled
                Return "ملغاة"
            Case LeaveStatus.Completed
                Return "مكتملة"
            Case Else
                Return "غير محدد"
        End Select
    End Function

    ''' <summary>
    ''' التحقق من صحة البيانات المدخلة
    ''' </summary>
    ''' <param name="value">القيمة</param>
    ''' <param name="fieldName">اسم الحقل</param>
    ''' <returns>صحيح إذا كانت البيانات صحيحة</returns>
    Private Function ValidateInput(value As String, fieldName As String) As Boolean
        If String.IsNullOrWhiteSpace(value) Then
            MessageBox.Show($"حقل {fieldName} مطلوب", "تحقق من البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return False
        End If
        Return True
    End Function

    ''' <summary>
    ''' التحقق من صحة التاريخ
    ''' </summary>
    ''' <param name="startDate">تاريخ البداية</param>
    ''' <param name="endDate">تاريخ النهاية</param>
    ''' <returns>صحيح إذا كانت التواريخ صحيحة</returns>
    Private Function ValidateDateRange(startDate As Date, endDate As Date) As Boolean
        If startDate > endDate Then
            MessageBox.Show("تاريخ البداية يجب أن يكون قبل تاريخ النهاية", "تحقق من التواريخ", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return False
        End If

        If startDate < Date.Today.AddYears(-1) Then
            MessageBox.Show("تاريخ البداية قديم جداً", "تحقق من التواريخ", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return False
        End If

        If endDate > Date.Today.AddYears(1) Then
            MessageBox.Show("تاريخ النهاية بعيد جداً", "تحقق من التواريخ", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return False
        End If

        Return True
    End Function

    #End Region
    
    #Region "دوال إدارة الموظفين"

    ''' <summary>
    ''' إضافة موظف جديد
    ''' </summary>
    Private Sub btnAddEmployee_Click(sender As Object, e As EventArgs) Handles btnAddEmployee.Click
        Try
            If ValidateEmployeeData() Then
                Dim newEmployee As New Employee(txtEmployeeId.Text.Trim(), txtEmployeeName.Text.Trim(), txtNationalId.Text.Trim()) With {
                    .JobTitle = txtJobTitle.Text.Trim(),
                    .JobGrade = cmbJobGrade.Text,
                    .BasicSalary = CDec(txtBasicSalary.Text),
                    .DailyAllowance = AssignmentCalculator.GetDailyAllowanceByGrade(cmbJobGrade.Text),
                    .Department = txtDepartment.Text.Trim(),
                    .HireDate = Date.Today,
                    .IsActive = True
                }

                employees.Add(newEmployee)
                RefreshAllLists()
                ClearEmployeeForm()

                MessageBox.Show($"تم إضافة الموظف '{newEmployee.FullName}' بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If
        Catch ex As Exception
            HandleError("خطأ في إضافة الموظف", ex)
        End Try
    End Sub
    
    ''' <summary>
    ''' التحقق من صحة بيانات الموظف
    ''' </summary>
    Private Function ValidateEmployeeData() As Boolean
        ' التحقق من رقم الموظف
        If Not ValidateInput(txtEmployeeId.Text, "رقم الموظف") Then
            txtEmployeeId.Focus()
            Return False
        End If

        ' التحقق من اسم الموظف
        If Not ValidateInput(txtEmployeeName.Text, "اسم الموظف") Then
            txtEmployeeName.Focus()
            Return False
        End If

        ' التحقق من رقم الهوية
        If Not ValidateInput(txtNationalId.Text, "رقم الهوية") Then
            txtNationalId.Focus()
            Return False
        End If

        If txtNationalId.Text.Trim().Length <> 10 Then
            MessageBox.Show("رقم الهوية يجب أن يكون 10 أرقام بالضبط", "تحقق من البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtNationalId.Focus()
            Return False
        End If

        If Not IsNumeric(txtNationalId.Text.Trim()) Then
            MessageBox.Show("رقم الهوية يجب أن يحتوي على أرقام فقط", "تحقق من البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtNationalId.Focus()
            Return False
        End If

        ' التحقق من المسمى الوظيفي
        If Not ValidateInput(txtJobTitle.Text, "المسمى الوظيفي") Then
            txtJobTitle.Focus()
            Return False
        End If

        ' التحقق من المرتبة
        If cmbJobGrade.SelectedIndex = -1 Then
            MessageBox.Show("يرجى اختيار المرتبة الوظيفية", "تحقق من البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            cmbJobGrade.Focus()
            Return False
        End If

        ' التحقق من الراتب الأساسي
        If Not ValidateInput(txtBasicSalary.Text, "الراتب الأساسي") Then
            txtBasicSalary.Focus()
            Return False
        End If

        Dim salary As Decimal
        If Not Decimal.TryParse(txtBasicSalary.Text.Trim(), salary) OrElse salary <= 0 Then
            MessageBox.Show("الراتب الأساسي يجب أن يكون رقم موجب", "تحقق من البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtBasicSalary.Focus()
            Return False
        End If

        ' التحقق من الإدارة
        If Not ValidateInput(txtDepartment.Text, "الإدارة") Then
            txtDepartment.Focus()
            Return False
        End If

        ' التحقق من عدم تكرار رقم الموظف
        If employees.Any(Function(e) e.EmployeeId.Equals(txtEmployeeId.Text.Trim(), StringComparison.OrdinalIgnoreCase)) Then
            MessageBox.Show("رقم الموظف موجود مسبقاً", "تحقق من البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtEmployeeId.Focus()
            Return False
        End If

        ' التحقق من عدم تكرار رقم الهوية
        If employees.Any(Function(e) e.NationalId = txtNationalId.Text.Trim()) Then
            MessageBox.Show("رقم الهوية موجود مسبقاً", "تحقق من البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtNationalId.Focus()
            Return False
        End If

        Return True
    End Function
    
    ''' <summary>
    ''' مسح نموذج الموظف
    ''' </summary>
    Private Sub ClearEmployeeForm()
        Try
            txtEmployeeId.Clear()
            txtEmployeeName.Clear()
            txtNationalId.Clear()
            txtJobTitle.Clear()
            cmbJobGrade.SelectedIndex = -1
            txtBasicSalary.Clear()
            txtDepartment.Clear()

            ' إعادة التركيز إلى أول حقل
            txtEmployeeId.Focus()

        Catch ex As Exception
            HandleError("خطأ في مسح نموذج الموظف", ex)
        End Try
    End Sub

    #End Region
    
    #Region "دوال حساب التكليفات"

    ''' <summary>
    ''' حساب التكليف
    ''' </summary>
    Private Sub btnCalculateAssignment_Click(sender As Object, e As EventArgs) Handles btnCalculateAssignment.Click
        Try
            If Not ValidateAssignmentData() Then Return

            Dim selectedEmployee As Employee = CType(cmbEmployee.SelectedItem, Employee)

            Dim assignment As New Assignment() With {
                .AssignmentId = GenerateAssignmentId(),
                .EmployeeId = selectedEmployee.EmployeeId,
                .EmployeeName = selectedEmployee.FullName,
                .StartDate = dtpAssignmentStart.Value.Date,
                .EndDate = dtpAssignmentEnd.Value.Date,
                .AssignmentLocation = txtAssignmentLocation.Text.Trim(),
                .AssignmentTo = txtAssignmentTo.Text.Trim(),
                .Purpose = txtPurpose.Text.Trim(),
                .DailyAllowance = selectedEmployee.DailyAllowance,
                .Status = CType(cmbAssignmentStatus.SelectedIndex, AssignmentStatus),
                .CreatedDate = Date.Now
            }

            Dim result As AssignmentCalculationResult = AssignmentCalculator.CalculateAssignmentDetails(assignment)

            ' عرض النتائج
            DisplayAssignmentResults(result)

            ' إضافة التكليف إلى القائمة
            assignments.Add(assignment)
            RefreshDataGrids()

            ' عرض رسالة نجاح
            MessageBox.Show($"تم حساب التكليف بنجاح{vbCrLf}عدد الأيام: {result.WorkingDays}{vbCrLf}إجمالي الانتداب: {result.TotalAllowance:N0} ريال",
                          "نجح الحساب", MessageBoxButtons.OK, MessageBoxIcon.Information)

            ' مسح البيانات للتكليف التالي
            ClearAssignmentForm()

        Catch ex As Exception
            HandleError("خطأ في حساب التكليف", ex)
        End Try
    End Sub

    ''' <summary>
    ''' التحقق من صحة بيانات التكليف
    ''' </summary>
    Private Function ValidateAssignmentData() As Boolean
        ' التحقق من اختيار الموظف
        If cmbEmployee.SelectedItem Is Nothing Then
            MessageBox.Show("يرجى اختيار موظف", "تحقق من البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            cmbEmployee.Focus()
            Return False
        End If

        ' التحقق من التواريخ
        If Not ValidateDateRange(dtpAssignmentStart.Value.Date, dtpAssignmentEnd.Value.Date) Then
            Return False
        End If

        ' التحقق من مكان التكليف
        If Not ValidateInput(txtAssignmentLocation.Text, "مكان التكليف") Then
            txtAssignmentLocation.Focus()
            Return False
        End If

        ' التحقق من الجهة المكلف إليها
        If Not ValidateInput(txtAssignmentTo.Text, "الجهة المكلف إليها") Then
            txtAssignmentTo.Focus()
            Return False
        End If

        ' التحقق من غرض التكليف
        If Not ValidateInput(txtPurpose.Text, "غرض التكليف") Then
            txtPurpose.Focus()
            Return False
        End If

        ' التحقق من حالة التكليف
        If cmbAssignmentStatus.SelectedIndex = -1 Then
            MessageBox.Show("يرجى اختيار حالة التكليف", "تحقق من البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            cmbAssignmentStatus.Focus()
            Return False
        End If

        Return True
    End Function

    ''' <summary>
    ''' عرض نتائج حساب التكليف
    ''' </summary>
    Private Sub DisplayAssignmentResults(result As AssignmentCalculationResult)
        Try
            lblAssignmentDays.Text = $"عدد أيام التكليف: {result.WorkingDays} يوم"
            lblTotalAllowance.Text = $"إجمالي الانتداب: {result.TotalAllowance:N0} ريال"
            lblExcludedDays.Text = $"الأيام المستثناة: {result.ExcludedFridays + result.ExcludedHolidays} يوم"
            lblDailyAllowance.Text = $"الانتداب اليومي: {result.DailyAllowance:N0} ريال"

            ' تلوين النتائج حسب القيمة
            If result.TotalAllowance > 5000 Then
                lblTotalAllowance.ForeColor = Color.Red
            ElseIf result.TotalAllowance > 2000 Then
                lblTotalAllowance.ForeColor = Color.Orange
            Else
                lblTotalAllowance.ForeColor = Color.Green
            End If

        Catch ex As Exception
            HandleError("خطأ في عرض نتائج التكليف", ex)
        End Try
    End Sub

    ''' <summary>
    ''' إنشاء رقم تكليف جديد
    ''' </summary>
    Private Function GenerateAssignmentId() As String
        Dim maxId As Integer = 0

        For Each assignment In assignments
            If assignment.AssignmentId.StartsWith("T") Then
                Dim idNumber As String = assignment.AssignmentId.Substring(1)
                Dim number As Integer
                If Integer.TryParse(idNumber, number) Then
                    maxId = Math.Max(maxId, number)
                End If
            End If
        Next

        Return $"T{(maxId + 1):D3}"
    End Function

    ''' <summary>
    ''' مسح نموذج التكليف
    ''' </summary>
    Private Sub ClearAssignmentForm()
        Try
            txtAssignmentLocation.Clear()
            txtAssignmentTo.Clear()
            txtPurpose.Clear()
            cmbEmployee.SelectedIndex = -1
            cmbAssignmentStatus.SelectedIndex = 0
            dtpAssignmentStart.Value = Date.Today
            dtpAssignmentEnd.Value = Date.Today.AddDays(7)

            ' إعادة تعيين النتائج
            lblAssignmentDays.Text = "عدد أيام التكليف: --"
            lblTotalAllowance.Text = "إجمالي الانتداب: --"
            lblExcludedDays.Text = "الأيام المستثناة: --"
            lblDailyAllowance.Text = "الانتداب اليومي: --"

        Catch ex As Exception
            HandleError("خطأ في مسح نموذج التكليف", ex)
        End Try
    End Sub

    #End Region

    #Region "دوال حساب الإجازات"

    ''' <summary>
    ''' حساب الإجازة
    ''' </summary>
    Private Sub btnCalculateLeave_Click(sender As Object, e As EventArgs) Handles btnCalculateLeave.Click
        Try
            If Not ValidateLeaveData() Then Return

            Dim selectedEmployee As Employee = CType(cmbLeaveEmployee.SelectedItem, Employee)
            Dim leaveType As LeaveType = CType(cmbLeaveType.SelectedIndex, LeaveType)

            Dim leave As New Leave() With {
                .LeaveId = GenerateLeaveId(),
                .EmployeeId = selectedEmployee.EmployeeId,
                .EmployeeName = selectedEmployee.FullName,
                .LeaveType = leaveType,
                .StartDate = dtpLeaveStart.Value.Date,
                .EndDate = dtpLeaveEnd.Value.Date,
                .Reason = txtLeaveReason.Text.Trim(),
                .Status = LeaveStatus.Pending,
                .ApplicationDate = Date.Now,
                .IsPaid = (leaveType <> LeaveType.Unpaid)
            }

            Dim result As LeaveCalculationResult = LeaveCalculator.CalculateLeaveDetails(leave, selectedEmployee)

            ' عرض النتائج
            DisplayLeaveResults(result, selectedEmployee)

            ' التحقق من الرصيد
            If Not result.HasSufficientBalance AndAlso leaveType <> LeaveType.Unpaid Then
                Dim confirmResult = MessageBox.Show(
                    $"تحذير: الرصيد الحالي ({result.CurrentBalance} يوم) غير كافي للإجازة المطلوبة ({result.WorkingDays} يوم).{vbCrLf}{vbCrLf}هل تريد المتابعة؟ سيتم خصم الأيام الإضافية من الراتب.",
                    "رصيد غير كافي", MessageBoxButtons.YesNo, MessageBoxIcon.Warning)

                If confirmResult = DialogResult.No Then Return
            End If

            ' إضافة الإجازة إلى القائمة
            leaves.Add(leave)

            ' تحديث رصيد الموظف
            UpdateEmployeeLeaveBalance(selectedEmployee, leaveType, result.WorkingDays)

            RefreshDataGrids()
            RefreshEmployeeLists()

            ' عرض رسالة نجاح
            MessageBox.Show($"تم حساب الإجازة بنجاح{vbCrLf}عدد الأيام: {result.WorkingDays}{vbCrLf}الرصيد المتبقي: {result.BalanceAfterLeave} يوم",
                          "نجح الحساب", MessageBoxButtons.OK, MessageBoxIcon.Information)

            ' مسح البيانات للإجازة التالية
            ClearLeaveForm()

        Catch ex As Exception
            HandleError("خطأ في حساب الإجازة", ex)
        End Try
    End Sub

    ''' <summary>
    ''' التحقق من صحة بيانات الإجازة
    ''' </summary>
    Private Function ValidateLeaveData() As Boolean
        ' التحقق من اختيار الموظف
        If cmbLeaveEmployee.SelectedItem Is Nothing Then
            MessageBox.Show("يرجى اختيار موظف", "تحقق من البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            cmbLeaveEmployee.Focus()
            Return False
        End If

        ' التحقق من نوع الإجازة
        If cmbLeaveType.SelectedIndex = -1 Then
            MessageBox.Show("يرجى اختيار نوع الإجازة", "تحقق من البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            cmbLeaveType.Focus()
            Return False
        End If

        ' التحقق من التواريخ
        If Not ValidateDateRange(dtpLeaveStart.Value.Date, dtpLeaveEnd.Value.Date) Then
            Return False
        End If

        ' التحقق من سبب الإجازة
        If Not ValidateInput(txtLeaveReason.Text, "سبب الإجازة") Then
            txtLeaveReason.Focus()
            Return False
        End If

        ' التحقق من عدم تداخل الإجازات
        Dim selectedEmployee As Employee = CType(cmbLeaveEmployee.SelectedItem, Employee)
        Dim overlappingLeaves = leaves.Where(Function(l) l.EmployeeId = selectedEmployee.EmployeeId AndAlso
                                           l.Status <> LeaveStatus.Cancelled AndAlso
                                           l.Status <> LeaveStatus.Rejected AndAlso
                                           ((l.StartDate <= dtpLeaveStart.Value.Date AndAlso l.EndDate >= dtpLeaveStart.Value.Date) OrElse
                                            (l.StartDate <= dtpLeaveEnd.Value.Date AndAlso l.EndDate >= dtpLeaveEnd.Value.Date) OrElse
                                            (l.StartDate >= dtpLeaveStart.Value.Date AndAlso l.EndDate <= dtpLeaveEnd.Value.Date)))

        If overlappingLeaves.Any() Then
            MessageBox.Show("يوجد تداخل مع إجازة أخرى في نفس الفترة", "تحقق من التواريخ", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return False
        End If

        Return True
    End Function

    ''' <summary>
    ''' عرض نتائج حساب الإجازة
    ''' </summary>
    Private Sub DisplayLeaveResults(result As LeaveCalculationResult, employee As Employee)
        Try
            lblLeaveDays.Text = $"عدد أيام الإجازة: {result.WorkingDays} يوم"
            lblCurrentBalance.Text = $"الرصيد الحالي: {result.CurrentBalance} يوم"
            lblBalanceAfter.Text = $"الرصيد بعد الإجازة: {result.BalanceAfterLeave} يوم"

            ' عرض تفاصيل الأرصدة
            lblAnnualBalance.Text = $"رصيد الإجازة الاعتيادية: {employee.AnnualLeaveBalance} يوم"
            lblSickBalance.Text = $"رصيد الإجازة المرضية: {employee.SickLeaveBalance} يوم"
            lblEmergencyBalance.Text = $"رصيد الإجازة الاضطرارية: {employee.EmergencyLeaveBalance} يوم"

            ' تلوين النتائج حسب الرصيد
            If result.BalanceAfterLeave < 5 Then
                lblBalanceAfter.ForeColor = Color.Red
            ElseIf result.BalanceAfterLeave < 10 Then
                lblBalanceAfter.ForeColor = Color.Orange
            Else
                lblBalanceAfter.ForeColor = Color.Green
            End If

            ' عرض تحذير إذا كان الرصيد غير كافي
            If Not result.HasSufficientBalance Then
                lblBalanceAfter.Text &= " (غير كافي)"
                lblBalanceAfter.ForeColor = Color.Red
            End If

        Catch ex As Exception
            HandleError("خطأ في عرض نتائج الإجازة", ex)
        End Try
    End Sub

    ''' <summary>
    ''' تحديث رصيد الإجازة للموظف
    ''' </summary>
    Private Sub UpdateEmployeeLeaveBalance(employee As Employee, leaveType As LeaveType, usedDays As Integer)
        Try
            Select Case leaveType
                Case LeaveType.Annual
                    employee.AnnualLeaveBalance = Math.Max(0, employee.AnnualLeaveBalance - usedDays)
                Case LeaveType.Sick
                    employee.SickLeaveBalance = Math.Max(0, employee.SickLeaveBalance - usedDays)
                Case LeaveType.Emergency
                    employee.EmergencyLeaveBalance = Math.Max(0, employee.EmergencyLeaveBalance - usedDays)
                ' إجازة الأمومة والأبوة والإجازة بدون راتب لا تؤثر على الأرصدة الأخرى
            End Select

        Catch ex As Exception
            HandleError("خطأ في تحديث رصيد الإجازة", ex)
        End Try
    End Sub

    ''' <summary>
    ''' إنشاء رقم إجازة جديد
    ''' </summary>
    Private Function GenerateLeaveId() As String
        Dim maxId As Integer = 0

        For Each leaveItem In leaves
            If leaveItem.LeaveId.StartsWith("L") Then
                Dim idNumber As String = leaveItem.LeaveId.Substring(1)
                Dim number As Integer
                If Integer.TryParse(idNumber, number) Then
                    maxId = Math.Max(maxId, number)
                End If
            End If
        Next

        Return $"L{(maxId + 1):D3}"
    End Function

    ''' <summary>
    ''' مسح نموذج الإجازة
    ''' </summary>
    Private Sub ClearLeaveForm()
        Try
            txtLeaveReason.Clear()
            cmbLeaveEmployee.SelectedIndex = -1
            cmbLeaveType.SelectedIndex = -1
            dtpLeaveStart.Value = Date.Today
            dtpLeaveEnd.Value = Date.Today.AddDays(3)

            ' إعادة تعيين النتائج
            lblLeaveDays.Text = "عدد أيام الإجازة: --"
            lblCurrentBalance.Text = "الرصيد الحالي: --"
            lblBalanceAfter.Text = "الرصيد بعد الإجازة: --"
            lblAnnualBalance.Text = "رصيد الإجازة الاعتيادية: --"
            lblSickBalance.Text = "رصيد الإجازة المرضية: --"
            lblEmergencyBalance.Text = "رصيد الإجازة الاضطرارية: --"

        Catch ex As Exception
            HandleError("خطأ في مسح نموذج الإجازة", ex)
        End Try
    End Sub

    #End Region

    #Region "دوال التقارير"

    ''' <summary>
    ''' إنتاج تقرير التكليفات
    ''' </summary>
    Private Sub btnAssignmentReport_Click(sender As Object, e As EventArgs) Handles btnAssignmentReport.Click
        Try
            If assignments.Count = 0 Then
                MessageBox.Show("لا توجد تكليفات لإنتاج التقرير", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return
            End If

            ' عرض رسالة انتظار
            Me.Cursor = Cursors.WaitCursor

            Dim htmlReport As String = ReportGenerator.GenerateAssignmentReport(assignments, employees)
            Dim fileName As String = $"تقرير_التكليفات_{Date.Now:yyyyMMdd_HHmmss}.html"

            ReportGenerator.SaveHtmlReport(htmlReport, fileName)

            Me.Cursor = Cursors.Default

            MessageBox.Show($"تم إنتاج تقرير التكليفات بنجاح{vbCrLf}الملف: {fileName}", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)

        Catch ex As Exception
            Me.Cursor = Cursors.Default
            HandleError("خطأ في إنتاج تقرير التكليفات", ex)
        End Try
    End Sub

    ''' <summary>
    ''' إنتاج تقرير الإجازات
    ''' </summary>
    Private Sub btnLeaveReport_Click(sender As Object, e As EventArgs) Handles btnLeaveReport.Click
        Try
            If leaves.Count = 0 Then
                MessageBox.Show("لا توجد إجازات لإنتاج التقرير", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return
            End If

            ' عرض رسالة انتظار
            Me.Cursor = Cursors.WaitCursor

            Dim htmlReport As String = ReportGenerator.GenerateLeaveReport(leaves, employees)
            Dim fileName As String = $"تقرير_الإجازات_{Date.Now:yyyyMMdd_HHmmss}.html"

            ReportGenerator.SaveHtmlReport(htmlReport, fileName)

            Me.Cursor = Cursors.Default

            MessageBox.Show($"تم إنتاج تقرير الإجازات بنجاح{vbCrLf}الملف: {fileName}", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)

        Catch ex As Exception
            Me.Cursor = Cursors.Default
            HandleError("خطأ في إنتاج تقرير الإجازات", ex)
        End Try
    End Sub

    #End Region

    #Region "أحداث العناصر"

    ''' <summary>
    ''' عرض تفاصيل الموظف المحدد للتكليف
    ''' </summary>
    Private Sub cmbEmployee_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbEmployee.SelectedIndexChanged
        Try
            If isLoading Then Return

            If cmbEmployee.SelectedItem IsNot Nothing Then
                Dim selectedEmployee As Employee = CType(cmbEmployee.SelectedItem, Employee)

                ' تحديث قيمة الانتداب اليومي
                lblDailyAllowance.Text = $"الانتداب اليومي: {selectedEmployee.DailyAllowance:N0} ريال"
                lblDailyAllowance.ForeColor = Color.FromArgb(0, 123, 255)

                ' عرض معلومات إضافية
                Dim tooltip As String = $"الموظف: {selectedEmployee.FullName}{vbCrLf}" &
                                       $"المرتبة: {selectedEmployee.JobGrade}{vbCrLf}" &
                                       $"الإدارة: {selectedEmployee.Department}{vbCrLf}" &
                                       $"الانتداب اليومي: {selectedEmployee.DailyAllowance:N0} ريال"

                ToolTip1.SetToolTip(cmbEmployee, tooltip)
            Else
                lblDailyAllowance.Text = "الانتداب اليومي: --"
                lblDailyAllowance.ForeColor = Color.Gray
            End If

        Catch ex As Exception
            HandleError("خطأ في عرض تفاصيل الموظف", ex)
        End Try
    End Sub

    ''' <summary>
    ''' عرض تفاصيل الموظف المحدد للإجازة
    ''' </summary>
    Private Sub cmbLeaveEmployee_SelectedIndexChanged(sender As Object, e As EventArgs) Handles cmbLeaveEmployee.SelectedIndexChanged
        Try
            If isLoading Then Return

            If cmbLeaveEmployee.SelectedItem IsNot Nothing Then
                Dim selectedEmployee As Employee = CType(cmbLeaveEmployee.SelectedItem, Employee)

                ' عرض أرصدة الإجازات
                lblAnnualBalance.Text = $"رصيد الإجازة الاعتيادية: {selectedEmployee.AnnualLeaveBalance} يوم"
                lblSickBalance.Text = $"رصيد الإجازة المرضية: {selectedEmployee.SickLeaveBalance} يوم"
                lblEmergencyBalance.Text = $"رصيد الإجازة الاضطرارية: {selectedEmployee.EmergencyLeaveBalance} يوم"

                ' تلوين الأرصدة حسب الكمية
                ColorizeBalanceLabel(lblAnnualBalance, selectedEmployee.AnnualLeaveBalance)
                ColorizeBalanceLabel(lblSickBalance, selectedEmployee.SickLeaveBalance)
                ColorizeBalanceLabel(lblEmergencyBalance, selectedEmployee.EmergencyLeaveBalance)
            Else
                lblAnnualBalance.Text = "رصيد الإجازة الاعتيادية: --"
                lblSickBalance.Text = "رصيد الإجازة المرضية: --"
                lblEmergencyBalance.Text = "رصيد الإجازة الاضطرارية: --"

                lblAnnualBalance.ForeColor = Color.Gray
                lblSickBalance.ForeColor = Color.Gray
                lblEmergencyBalance.ForeColor = Color.Gray
            End If

        Catch ex As Exception
            HandleError("خطأ في عرض تفاصيل الموظف", ex)
        End Try
    End Sub

    ''' <summary>
    ''' تلوين تسمية الرصيد حسب الكمية
    ''' </summary>
    Private Sub ColorizeBalanceLabel(label As Label, balance As Integer)
        If balance <= 5 Then
            label.ForeColor = Color.Red
        ElseIf balance <= 10 Then
            label.ForeColor = Color.Orange
        Else
            label.ForeColor = Color.Green
        End If
    End Sub

    ''' <summary>
    ''' تشغيل اختبارات النظام
    ''' </summary>
    Private Sub btnRunTests_Click(sender As Object, e As EventArgs) Handles btnRunTests.Click
        Try
            ' تشغيل الاختبارات في نافذة منفصلة
            Dim result As DialogResult = MessageBox.Show("هل تريد تشغيل اختبارات النظام؟" & vbCrLf & "ستظهر النتائج في نافذة وحدة التحكم.",
                                                       "تشغيل الاختبارات", MessageBoxButtons.YesNo, MessageBoxIcon.Question)

            If result = DialogResult.Yes Then
                Me.Cursor = Cursors.WaitCursor

                ' إنشاء نافذة وحدة التحكم
                AllocConsole()
                CalculatorTests.RunAllTests()

                Me.Cursor = Cursors.Default

                MessageBox.Show("تم تشغيل الاختبارات بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If

        Catch ex As Exception
            Me.Cursor = Cursors.Default
            HandleError("خطأ في تشغيل الاختبارات", ex)
        End Try
    End Sub

    ''' <summary>
    ''' استيراد دالة إنشاء وحدة التحكم من Windows API
    ''' </summary>
    <System.Runtime.InteropServices.DllImport("kernel32.dll")>
    Private Shared Function AllocConsole() As Boolean
    End Function

    #End Region

End Class
