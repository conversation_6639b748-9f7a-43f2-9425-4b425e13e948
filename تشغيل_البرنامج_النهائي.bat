@echo off
chcp 65001 >nul
cls
echo.
echo ========================================
echo    برنامج حسابات الموظفين - التكليفات والإجازات
echo ========================================
echo.

echo جاري التحقق من متطلبات النظام...

REM التحقق من وجود .NET
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: .NET غير مثبت على النظام
    echo يرجى تثبيت .NET 8.0 أو أحدث من:
    echo https://dotnet.microsoft.com/download
    echo.
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('dotnet --version') do set DOTNET_VERSION=%%i
echo ✅ .NET الإصدار: %DOTNET_VERSION%

echo.
echo جاري بناء المشروع...
dotnet clean >nul 2>&1
dotnet build --configuration Release --verbosity quiet
if errorlevel 1 (
    echo ❌ فشل في بناء المشروع
    echo يرجى التحقق من الأخطاء أعلاه
    echo.
    pause
    exit /b 1
)

echo ✅ تم بناء المشروع بنجاح

echo.
echo جاري تشغيل البرنامج...
echo ملاحظة: سيفتح البرنامج في نافذة منفصلة

REM تشغيل البرنامج في الخلفية
start "" /min cmd /c "dotnet run --configuration Release"

echo.
echo ✅ تم تشغيل البرنامج بنجاح!
echo.
echo إذا لم يظهر البرنامج، تحقق من:
echo - أن Windows Defender لا يحجب البرنامج
echo - أن البرنامج لم يفتح خلف النوافذ الأخرى
echo.
echo يمكنك إغلاق هذه النافذة الآن.
echo.
timeout /t 5 >nul
