@echo off
cls
echo.
echo ========================================
echo    Employee Assignment System
echo ========================================
echo.

echo Checking system requirements...

REM Check if .NET is installed
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: .NET is not installed
    echo Please install .NET 8.0 or newer from:
    echo https://dotnet.microsoft.com/download
    echo.
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('dotnet --version') do set DOTNET_VERSION=%%i
echo .NET Version: %DOTNET_VERSION%

echo.
echo Building project...
dotnet clean >nul 2>&1
dotnet build --configuration Release --verbosity quiet
if errorlevel 1 (
    echo BUILD FAILED
    echo Please check the errors above
    echo.
    pause
    exit /b 1
)

echo Build successful!

echo.
echo Starting application...
echo Note: The program will open in a separate window

REM Run the application
dotnet run --configuration Release

echo.
echo Application started successfully!
echo You can close this window now.
echo.
pause
